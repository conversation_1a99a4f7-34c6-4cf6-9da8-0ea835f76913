import UIKit
import ZLImageEditor
import AVFoundation
import SnapKit
import PhotosUI
import Photos

protocol LockScreenCameraViewControllerDelegate: AnyObject {
    func cameraViewControllerDidRequestShowFriendList(_ viewController: LockScreenCameraViewController)
    func cameraViewControllerDidRequestShowSettings(_ viewController: LockScreenCameraViewController)
}

// MARK: - 主控制器
class LockScreenCameraViewController: BaseViewController, PHPickerViewControllerDelegate, UIScrollViewDelegate {
    
    weak var delegate: LockScreenCameraViewControllerDelegate?
    private let topBar = ACTopBarView()
    private let cameraPreview = CameraPreviewView()
    private let timeView = ACTimeView()
    private let preViewBottomView = ACPreViewBottomView()

    let previewImageView = UIImageView()
    private let zoomIndicator = ZoomLevelIndicator()

    private let captureSession = AVCaptureSession()
    private let photoOutput = AVCapturePhotoOutput()
    /// 串行队列，保证 Session 相关操作按顺序执行，避免竞争
    private let sessionQueue = DispatchQueue(label: "com.looklock.camera.session")
    /// KVO 观察，用来感知 session.isRunning 状态变化
    private var sessionRunningObserver: NSKeyValueObservation?
    private var videoDeviceInput: AVCaptureDeviceInput?
    private var currentZoomFactor: CGFloat = 1.0
    private var currentFlashMode: AVCaptureDevice.FlashMode = .auto
    
    // 引导毛玻璃遮罩
    private var automationOverlay: UIView?
    
    // MARK: - 绑定邮箱横幅

    private var emailBanner: UIView?

    let bottomBar = ACBottomBarView()
    
    // Add weak reference to container
    weak var containerViewController: ACMainContainerViewController?

    private var isPreviewing = false
    private var friendIDsToSync: [String] = []
    internal var isEditingMode = false

    // 单次投送目标好友
    private var singleSendFriend: FriendItem?

    // 跑马灯 loading
    private var marqueeView: MarqueeBorderView?

    // Crop & Scroll support
    let cropScrollView = UIScrollView()
    let gridOverlayView = GridOverlayView()
    var lastCropRect: CGRect?
    private(set) var isCropEnabled = false

    // Flag: 正在投送中，防止重复点击
    private var isSending = false

    // Helper: 找到顶层 ACMainContainerViewController
    private func mainContainer() -> ACMainContainerViewController? {
        var parentVC: UIViewController? = self
        while let p = parentVC {
            if let main = p as? ACMainContainerViewController { return main }
            parentVC = p.parent
        }
        return nil
    }

    override func setupUI() {
        super.setupUI()
        view.backgroundColor = .black
        let containerStackView = UIStackView()
        containerStackView.axis = .vertical
        containerStackView.distribution = .fill
        containerStackView.spacing = 0
        view.addSubview(containerStackView)
        
        containerStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(SafeAreaManager.shared.statusBarHeight)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().inset(SafeAreaManager.shared.bottomSafeAreaInset)
        }
        
        // 顶部栏
        containerStackView.addArrangedSubview(topBar)
        topBar.snp.makeConstraints { make in
            make.height.equalTo(40)
        }
        let cameraPreviewContainer = UIView()
        cameraPreviewContainer.backgroundColor = .clear
        containerStackView.addArrangedSubview(cameraPreviewContainer)

        // 相机预览
        cameraPreview.setSession(captureSession)
        cameraPreview.setCorner(radius: 30)
        cameraPreviewContainer.addSubview(cameraPreview)
        cameraPreview.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(23)
            make.centerX.equalToSuperview()
            make.width.equalTo(cameraPreview.snp.height).multipliedBy(9.0/19.5)
        }
        
        // 时间显示
        cameraPreview.addSubview(timeView)
        timeView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(46)
            make.centerX.equalToSuperview()
        }

        // 预览底部栏
        cameraPreview.addSubview(preViewBottomView)
        preViewBottomView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(33)
            make.left.right.equalToSuperview().inset(33)
        }
        
        // Flash icon tap to toggle
        let flashTap = UITapGestureRecognizer(target: self, action: #selector(handleFlashToggle))
        preViewBottomView.leftImgView.isUserInteractionEnabled = true
        preViewBottomView.leftImgView.addGestureRecognizer(flashTap)
        
        // 底部栏
        containerStackView.addArrangedSubview(bottomBar)
        
        // 预览 ScrollView & 图片
        cameraPreview.clipsToBounds = true
        cameraPreview.addSubview(cropScrollView)
        cropScrollView.isHidden = true
        cropScrollView.isUserInteractionEnabled = false
        cropScrollView.backgroundColor = .black
        cropScrollView.delegate = self
        cropScrollView.showsHorizontalScrollIndicator = false
        cropScrollView.showsVerticalScrollIndicator = false
        cropScrollView.bouncesZoom = false
        cropScrollView.decelerationRate = .fast
        cropScrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        cropScrollView.addSubview(previewImageView)
        previewImageView.contentMode = .scaleAspectFit
        previewImageView.clipsToBounds = true

        // 网格引导线覆盖
        cameraPreview.addSubview(gridOverlayView)
        gridOverlayView.isHidden = true
        gridOverlayView.backgroundColor = .clear
        gridOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 双击缩放
        let doubleTap = UITapGestureRecognizer(target: self, action: #selector(handleCropDoubleTap))
        doubleTap.numberOfTapsRequired = 2
        cropScrollView.addGestureRecognizer(doubleTap)
        
        // 绑定事件
        topBar.friendsButton.addTarget(self, action: #selector(friendsButtonTapped), for: .touchUpInside)
        topBar.addFriendButton.addTarget(self, action: #selector(addFriendButtonTapped), for: .touchUpInside)
        topBar.avatarButton.addTarget(self, action: #selector(avatarButtonTapped), for: .touchUpInside)
        topBar.visibilityControl.addTarget(self, action: #selector(visibilityButtonTapped), for: .touchUpInside)
        bottomBar.onShutterButtonTapped = { [weak self] in self?.handleShutterButton() }
        bottomBar.onThumbnailButtonTapped = { [weak self] in self?.handleThumbnailButton() }
        bottomBar.onSwitchCameraButtonTapped = { [weak self] in self?.handleSwitchCameraButton() }
        
        // 添加点击事件
        let tap = UITapGestureRecognizer(target: self, action: #selector(historyViewTapped))
        bottomBar.historyView.isUserInteractionEnabled = true
        bottomBar.historyView.addGestureRecognizer(tap)
        
        setupCamera()
        setupGestures()

        // 监听单次投送选择
        NotificationCenter.default.addObserver(self, selector: #selector(handleSingleSendSelected(_:)), name: Notification.Name("SingleSendFriendSelected"), object: nil)

        // 监听单次投送选择
        NotificationCenter.default.addObserver(self, selector: #selector(handleSingleSendSelected(_:)), name: Notification.Name("SingleSendFriendSelected"), object: nil)
        // 监听教程进度变化
        NotificationCenter.default.addObserver(self, selector: #selector(tutorialProgressDidChange), name: .tutorialProgressDidChange, object: nil)

        // 初次检查自动化引导
        showAutomationOverlayIfNeeded()
        showEmailBannerIfNeeded()
        
        // 处理取消事件
        topBar.onCancelSingleSend = { [weak self] in
            self?.cancelSingleSendMode()
        }
    }
    
    //导航栏隐藏
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        startCamera()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopCamera()
        //导航栏显示
        navigationController?.setNavigationBarHidden(false, animated: animated)
    }
    
    private func setupCamera() {
        captureSession.beginConfiguration()
        captureSession.sessionPreset = .photo
        var position: AVCaptureDevice.Position = .back
        if UserService.shared.userSettings.keepLastCameraOrientation {
            position = UserService.shared.userSettings.lastCameraOrientationback ? .back : .front
        }
        if let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: position),
           let input = try? AVCaptureDeviceInput(device: device),
           captureSession.canAddInput(input),
           captureSession.canAddOutput(photoOutput) {
            captureSession.addInput(input)

            videoDeviceInput = input
            captureSession.addOutput(photoOutput)
        }
        
        captureSession.commitConfiguration()

        // 监听 session 的运行状态，及时更新快门按钮可用性
        sessionRunningObserver = captureSession.observe(\.isRunning,
            options: [.initial, .new]
        ) { [weak self] _, _ in
            DispatchQueue.main.async {
                self?.updateShutterAvailability()
            }
        }
    }
    
    func startCamera() {
        if isPreviewing {
            self.bottomBar.shutterButton.isEnabled = true
            self.bottomBar.outerBorder.alpha = 1.0
            return
        }
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            if !self.captureSession.isRunning {
                self.captureSession.startRunning()
            }
        }
    }
    
    func stopCamera() {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            if self.captureSession.isRunning {
                self.captureSession.stopRunning()
            }
        }
    }
    
    private func setupGestures() {
        let pinch = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch(_:)))
        cameraPreview.addGestureRecognizer(pinch)
    }
    
    @objc private func friendsButtonTapped() {
        delegate?.cameraViewControllerDidRequestShowFriendList(self)
    }
    
    @objc private func addFriendButtonTapped() {
        let addFriendVC = AddFriendViewController()
        self.present(addFriendVC, animated: true)
    }
    
    @objc private func visibilityButtonTapped() {
        let selectionVC = FriendSelectionViewController()
        selectionVC.modalPresentationStyle = .pageSheet
        selectionVC.onSelectionComplete = { [weak self] mode, selectedFriendIDs in
            guard let self = self else { return }
            self.friendIDsToSync = selectedFriendIDs
            self.updateVisibilityUI(mode: mode, selectedIDs: selectedFriendIDs)
        }
        present(selectionVC, animated: true)
    }
    
    private func updateVisibilityUI(mode: FriendSelectionViewController.SelectionMode, selectedIDs: [String]) {
        if mode == .all {
            topBar.updateVisibility(title: "camera_send_to_all_friends".localized, urls: [], extraCount: 0)
        } else {
            let friends = FriendRepository.shared.friends
            let avatars = selectedIDs.compactMap { id -> String? in
                friends.first(where: { $0.friendId == id })?.avatar
            }
            let extra = max(0, selectedIDs.count - 3)
            topBar.updateVisibility(title: "camera_send_to_friends".localized, urls: avatars, extraCount: extra)
        }
    }

    // 在界面加载时同步上次缓存状态
    private func applyLastVisibilitySelection() {
        if let (mode, ids) = FriendSelectionViewController.loadLastSelection() {
            self.friendIDsToSync = ids
            updateVisibilityUI(mode: mode, selectedIDs: ids)
        } else {
            // fallback to all friends
            self.friendIDsToSync = FriendRepository.shared.friends.map { $0.friendId }
            updateVisibilityUI(mode: .all, selectedIDs: friendIDsToSync)
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        // other existing setup
//        FriendRepository.shared.friendsPublisher
//            .receive(on: DispatchQueue.main)
//            .sink { [weak self] list in
//                guard let self = self else { return }
//                if list.isEmpty {
//                    // 仍无好友，保持高亮
//                    self.topBar.activateInviteHighlight()
//                } else {
//                    // 有好友 → 恢复正常
//                    self.topBar.deactivateInviteHighlight()
//                }
//            }
    }
    
    @objc private func avatarButtonTapped() {
        delegate?.cameraViewControllerDidRequestShowSettings(self)
    }
    
    @objc private func capturePhoto() {
        // Session 未就绪时忽略点击，避免 FigCaptureSession 断言
        guard captureSession.isRunning else {
            return
        }

        Haptic.impact(.light)
        
        let settings = AVCapturePhotoSettings()
        settings.flashMode = currentFlashMode
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    /// 打开系统相册（iOS14+ 使用 PHPicker，呈现为底部中等 detent，保持相机画面在上方可见）
    @objc private func openPhotoLibrary() {
        requestPhotoLibraryAccess { [weak self] granted in
            guard let self = self else { return }
            if granted {
                self.presentPhotoPicker()
            } else {
                self.showError(with: "camera_photo_access_denied_message".localized)
            }
        }
    }
    
    /// 统一检测并请求用户相册权限（iOS14+ 使用 .readWrite）
    private func requestPhotoLibraryAccess(completion: @escaping (Bool) -> Void) {
        // 本项目最低 iOS 16，仅保留 .readWrite 路径即可
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        switch status {
        case .authorized, .limited:
            completion(true)
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                DispatchQueue.main.async {
                    completion(newStatus == .authorized || newStatus == .limited)
                }
            }
        default:
            completion(false)
        }
    }
    
    /// 实际弹出 PHPicker
    private func presentPhotoPicker() {
        var configuration = PHPickerConfiguration(photoLibrary: .shared())
        configuration.filter = .images
        configuration.selectionLimit = 1
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = self
        if let sheet = picker.sheetPresentationController {
            sheet.detents = [.medium(), .large()]
            sheet.prefersGrabberVisible = true
            sheet.selectedDetentIdentifier = .medium
        }
        present(picker, animated: true)
    }
    
    @objc private func switchCamera() {
        guard let currentInput = videoDeviceInput else { return }
        
        captureSession.beginConfiguration()
        captureSession.removeInput(currentInput)
        
        let newPosition: AVCaptureDevice.Position = (currentInput.device.position == .back) ? .front : .back
        if let newDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: newPosition),
           let newInput = try? AVCaptureDeviceInput(device: newDevice),
           captureSession.canAddInput(newInput) {
            captureSession.addInput(newInput)
            videoDeviceInput = newInput
        }
        
        captureSession.commitConfiguration()

        if let connection = photoOutput.connection(with: .video),
           videoDeviceInput?.device.position == .front {
            connection.isVideoMirrored = true
        }
        var userSettings =  UserService.shared.userSettings
        userSettings.lastCameraOrientationback = videoDeviceInput?.device.position == .back
        Task {
            try? await UserService.shared.updateUserSettings(userSettings)
        }
    }
    
    @objc private func handlePinch(_ gesture: UIPinchGestureRecognizer) {
        guard let device = videoDeviceInput?.device else { return }
        
        if gesture.state == .changed {
            do {
                try device.lockForConfiguration()
                let maxZoom = min(device.activeFormat.videoMaxZoomFactor, 6.0)
                var newZoom = device.videoZoomFactor * gesture.scale
                newZoom = max(1.0, min(newZoom, maxZoom))
                device.videoZoomFactor = newZoom
                currentZoomFactor = newZoom
                zoomIndicator.setZoomLevel(newZoom)
                gesture.scale = 1.0
                device.unlockForConfiguration()
            } catch {
                print("⚠️ 缩放失败: \(error)")
            }
        }
    }
    
    private func showPreview(image: UIImage, enableCrop: Bool = false) {
        isCropEnabled = enableCrop
        stopCamera()

        cropScrollView.isHidden = false
        cropScrollView.isUserInteractionEnabled = enableCrop
        previewImageView.isHidden = false

        // 直接使用选取的图片，避免任何额外拷贝
        previewImageView.image = image

        if enableCrop {
            // Configure scrollView & imageView for cropping
            previewImageView.contentMode = .scaleAspectFit
            previewImageView.frame = CGRect(origin: .zero, size: image.size)
            cropScrollView.contentSize = image.size

            let viewSize = cameraPreview.bounds.size
            let scaleX = viewSize.width / image.size.width
            let scaleY = viewSize.height / image.size.height
            let minScale = max(scaleX, scaleY)
            cropScrollView.minimumZoomScale = minScale
            cropScrollView.maximumZoomScale = minScale * 4
            cropScrollView.zoomScale = minScale
            cropScrollView.contentOffset = CGPoint(
                x: (image.size.width * minScale - viewSize.width) / 2,
                y: (image.size.height * minScale - viewSize.height) / 2
            )
        } else {
            // Display image full with no cropping; reset zoom & offset
            previewImageView.contentMode = .scaleAspectFill

            // 让 imageView 填充 scrollView 区域
            previewImageView.frame = cropScrollView.bounds

            cropScrollView.contentSize = cropScrollView.bounds.size
            cropScrollView.minimumZoomScale = 1
            cropScrollView.maximumZoomScale = 1
            cropScrollView.zoomScale = 1
            cropScrollView.contentOffset = .zero
        }

        isPreviewing = true
        updateUIVisibility(isPreviewing: true)


        cropScrollView.alpha = enableCrop ? 0 : cropScrollView.alpha
        UIView.animate(withDuration: 0.25) {
            if enableCrop { self.cropScrollView.alpha = 1.0 }
            self.bottomBar.updateForMode(.preview)
        }

        if enableCrop {
            (self.parent as? LockScreenCameraContainerViewController)?.setVerticalScrolling(false)
            mainContainer()?.disableScrolling()
            showCropTip()
        }

    }
    
    private func resetCrop() {
        // Reset scroll view state
        cropScrollView.setZoomScale(1, animated: false)
        cropScrollView.minimumZoomScale = 1
        cropScrollView.maximumZoomScale = 1
        cropScrollView.contentOffset = .zero
        // 同时重置 transform，彻底清空上次缩放留下的 CGAffineTransform
        previewImageView.transform = .identity
        previewImageView.frame = .zero
        previewImageView.contentMode = .scaleAspectFit
        cropScrollView.contentSize = .zero
    }
    
    private func closePreview() {
        previewImageView.image = nil
        previewImageView.isHidden = true
        cropScrollView.isHidden = true
        isCropEnabled = false
        lastCropRect = nil
        isPreviewing = false
        updateUIVisibility(isPreviewing: false)
        bottomBar.updateForMode(.normal)
        startCamera()

        // 恢复父容器滚动
        (self.parent as? LockScreenCameraContainerViewController)?.setVerticalScrolling(true)

        // 恢复主容器滚动
        mainContainer()?.enableScrolling()

        resetCrop()
    }
    
    private func sendPreview() {
        guard let original = previewImageView.image else { return }
        let finalImage = isCropEnabled ? croppedImageForSending() : original
        if UserService.shared.userSettings.syncPreference {
            promptForSyncPreference(image: finalImage)
        } else {
            if UserService.shared.userSettings.syncToLockScreen {
                updateFriendsToSync()
            }
            uploadAndSyncImage(finalImage)
        }
    }
    private func updateFriendsToSync() {
        var totalIds = self.friendIDsToSync
        if let userId = UserService.shared.currentUser?.userId {
            totalIds.append(userId)
        }
        self.friendIDsToSync = totalIds
    }
    
    private func promptForSyncPreference(image: UIImage) {
        let optionView = CheckboxOptionView(text: "camera_sync_preference_always".localized)
        var currentSettings = UserService.shared.userSettings
        func optionViewUpdate(_ choose : Bool) {
            if optionView.isSelected {
                currentSettings.syncPreference = false
            }
            currentSettings.syncToLockScreen = choose
            Task {
                try? await UserService.shared.updateUserSettings(currentSettings)
            }
        }
        let noAction = ACAlertAction(title: "camera_sync_preference_no".localized, style: .secondary) { [weak self] in
            optionViewUpdate(false)
            self?.uploadAndSyncImage(image)
        }
        
        let yesAction = ACAlertAction(title: "camera_sync_preference_yes".localized, style: .primary) { [weak self] in
            optionViewUpdate(true)
            self?.updateFriendsToSync()
            self?.uploadAndSyncImage(image)
        }
        
        let alertVC = ACAlertViewController(
            title: "camera_sync_preference_question".localized,
            message: nil,
            optionView: optionView,
            actions: [noAction, yesAction]
        )
        
        present(alertVC, animated: true)
    }

    @objc private func historyViewTapped() {
        // 通知容器滚动到历史页
        if let parent = self.parent as? LockScreenCameraContainerViewController {
            parent.scrollToHistoryPage(animated: true)
        }
    }
    
    private func handleShutterButton() {
        //没有好友的时候且在拍摄模式下中断这次点击，topBar.addFriendButton 会有个放大且的动画且，变成主题色
        if !isPreviewing, FriendRepository.shared.friends.isEmpty {
            topBar.startInviteHighlight()   //
            return                          // 不拍照
        }

        // 有好友，按原流程
        isPreviewing ? sendPreview() : capturePhoto()
    }
    
    private func handleThumbnailButton() {
        if isPreviewing {
            closePreview()
        } else {
            openPhotoLibrary()
        }
    }
    
    private func handleSwitchCameraButton() {
        if isPreviewing {
            startEditing()
        } else {
            switchCamera()
        }
    }
     
    
    func startEditing() {
        // 使用当前图片（若在裁剪模式则取裁剪后的），避免对大尺寸 imageView 做 snapshot 导致内存暴涨
        let baseImage: UIImage
        if isCropEnabled {
            baseImage = self.croppedImageForSending()
        } else {
            baseImage = previewImageView.image ?? UIImage()
        }

        presentImageEditor(for: baseImage) { [weak self] edited in
            // 编辑完直接更新图片，不再进入裁剪
            self?.isResendFromHistory = false
            self?.resetCrop()
            self?.showPreview(image: edited, enableCrop: false)
        }
    }

    
    private func updateUIVisibility(isPreviewing: Bool) {
        // 单次投送模式下不改变样式
        guard !topBar.isSingleSendActive else { return }
        if isPreviewing {
            applyLastVisibilitySelection()
        }
        topBar.transition(to: isPreviewing ? .preview : .camera)
        updateShutterAvailability()
    }
    
    private func uploadAndSyncImage(_ image: UIImage) {
        // 如果已经在发送中，直接忽略后续点击
        guard !isSending else { return }
        isSending = true
        // 禁用按钮避免重复点击
        bottomBar.shutterButton.isEnabled = false
        bottomBar.outerBorder.alpha = 0.3
        bottomBar.thumbnailButton.isEnabled = false       // ← 新增
        bottomBar.switchCameraButton.isEnabled = false    // ← 新增
        showMarqueeLoading()
        if self.isResendFromHistory {
            self.syncLockScreen(imageURL: isResendFromUrl, friendIDs: self.friendIDsToSync, isAgain: self.isResendFromHistory)
            return
        }
        // 1. Upload image to get URL
        let uploader = LockScreenImageUploaderService.shared
        uploader.uploadImage(image, progress: { progress in
            // You can update the loading indicator with progress here
        }) { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let imageURL):
                // 2. Call syncLockScreen API
                self.syncLockScreen(imageURL: imageURL, friendIDs: self.friendIDsToSync, isAgain: self.isResendFromHistory)
                
            case .failure(let error):
                // 上传失败，恢复状态
                self.isSending = false
                self.bottomBar.shutterButton.isEnabled = true
                self.bottomBar.outerBorder.alpha = 1.0
                self.bottomBar.thumbnailButton.isEnabled = true   // ← 新增
                self.bottomBar.switchCameraButton.isEnabled = true// ← 新增
                self.hideMarqueeLoading()
                self.showError(with: "camera_upload_failed".localized(with: error.localizedDescription))
                self.closePreview()
            }
        }
    }
    
    private func syncLockScreen(imageURL: String, friendIDs: [String], isAgain: Bool) {
        // marquee 已经显示
        NetworkManager.shared.syncLockScreen(lockScreenInfo: imageURL, toFriends: friendIDs, isAgain: isAgain) { [weak self] result in
            guard let self = self else { return }
            // 无论成功与否，先恢复按钮状态
            self.isSending = false
            self.bottomBar.shutterButton.isEnabled = true
            self.bottomBar.thumbnailButton.isEnabled = true   // ← 新增
            self.bottomBar.switchCameraButton.isEnabled = true// ← 新增
            self.bottomBar.outerBorder.alpha = 1.0

            self.hideMarqueeLoading()
            // 无论成功与否，请求完成后复位 resend 状态
            self.isResendFromHistory = false
            switch result {
            case .success:
                self.playSuccessFeedback()
                // 发送成功后退出单次投送模式
                self.cancelSingleSendMode()
                self.closePreview()
                // 使用通知方式刷新历史列表，减少耦合
                NotificationCenter.default.post(name: NSNotification.Name("RefreshLockScreenHistory"), object: nil)
            case .failure(let error):
                var errorMessage: String = ""
                switch error {
                case .custom(let customError):
                    errorMessage = customError
                case .serverError(code: _, message: let customError):
                    errorMessage = customError
                default:
                    break
                }
                self.showError(with: "camera_send_failed".localized(with: errorMessage))
            }
        }
    }
    
    private func showFirstTimeSyncSuccessAlert() {
        let laterAction = ACAlertAction(title: "camera_first_success_later".localized, style: .secondary)
        let addNowAction = ACAlertAction(title: "camera_first_success_add_now".localized, style: .primary) { [weak self] in
            let addFriendVC = AddFriendViewController()
            self?.present(addFriendVC, animated: true)
        }
        
        let alertVC = ACAlertViewController(
            title: "camera_first_success_title".localized,
            message: "camera_first_success_message".localized,
            actions: [laterAction, addNowAction]
        )
        
        present(alertVC, animated: true)
    }

    // MARK: - 单次投送通知处理
    @objc private func handleSingleSendSelected(_ notification: Notification) {
        guard let dict = notification.userInfo as? [String: Any],
              let friendId = dict["friendId"] as? String else { return }

        let avatar = dict["avatar"] as? String ?? ""
        let nickname = dict["nickname"] as? String ?? ""

        self.singleSendFriend = FriendItem(avatar: avatar, friendId: friendId, nickname: nickname)
        self.friendIDsToSync = [friendId]

        // 更新TopBar样式
        self.topBar.enterSingleSendMode(avatarURL: avatar, name: nickname)
        // 延迟动画，等主容器滚动完成后再执行
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.45) {
            self.topBar.pulseSingleSendContainer()
        }
    }

    private func cancelSingleSendMode() {
        if topBar.isSingleSendActive {
            topBar.exitSingleSendMode()
        }
        singleSendFriend = nil

        // 恢复最近一次分享范围（若有），并同步按钮文案
//        applyLastVisibilitySelection()
        updateUIVisibility(isPreviewing: isPreviewing)
    }

    deinit {
        NotificationCenter.default.removeObserver(self, name: Notification.Name("SingleSendFriendSelected"), object: nil)
        NotificationCenter.default.removeObserver(self, name: .tutorialProgressDidChange, object: nil)
        sessionRunningObserver?.invalidate()
    }

    // MARK: - Marquee Loading Helpers
    private func showMarqueeLoading() {
        guard marqueeView == nil else { return }
        let marquee = MarqueeBorderView(cornerRadius: 30, lineWidth: 4)
        cameraPreview.addSubview(marquee)
        marquee.frame = cameraPreview.bounds
        marquee.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        // 初始淡入
        marquee.alpha = 0
        UIView.animate(withDuration: 0.25) {
            marquee.alpha = 1
        }
        marqueeView = marquee
    }
    private func hideMarqueeLoading() {
        guard let marquee = marqueeView else { return }
        UIView.animate(withDuration: 0.25, animations: {
            marquee.alpha = 0
        }) { _ in
            marquee.removeFromSuperview()
        }
        marqueeView = nil
    }

    private func playSuccessFeedback() {
        // Only haptic tap feedback
        Haptic.notification(.success)
    }

    // MARK: - PHPicker Delegate
    @available(iOS 14.0, *)
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        picker.dismiss(animated: true)
        
        guard let first = results.first else { return }

        // 统一显示 loading
        showLoading()

        // 如果拿得到 assetIdentifier，优先走 PHAsset 路线（效率更高，可控尺寸）
        if let assetId = first.assetIdentifier {
            let assets = PHAsset.fetchAssets(withLocalIdentifiers: [assetId], options: nil)
            guard let asset = assets.firstObject else {
                hideLoading()
                return
            }

            let maxLongSide: CGFloat = 2600      // 约等于 1290×2796 屏幕的长边
            let wRatio = CGFloat(asset.pixelWidth)  / maxLongSide
            let hRatio = CGFloat(asset.pixelHeight) / maxLongSide
            let ratio  = max(wRatio, hRatio, 1)     // 确保只做缩小

            var targetSize = CGSize(
                width:  CGFloat(asset.pixelWidth)  / ratio,
                height: CGFloat(asset.pixelHeight) / ratio
            )
            targetSize.width  = min(targetSize.width,  maxLongSide)
            targetSize.height = min(targetSize.height, maxLongSide)
            
            // 请求选项：允许 iCloud 下载
            let options = PHImageRequestOptions()
            options.deliveryMode           = .highQualityFormat
            options.resizeMode             = .exact
            options.isNetworkAccessAllowed = true         // 关键：允许从 iCloud 拉取

            PHImageManager.default().requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFit,
                options: options
            ) { [weak self] image, info in
                guard let self = self else { return }

                let cancelled = (info?[PHImageCancelledKey] as? Bool) ?? false
                let error     = info?[PHImageErrorKey]     as? NSError

                // 成功：直接用缩略图
                if let img = image, !cancelled, error == nil {
                    self.hideLoading()
                    self.showPreview(image: img, enableCrop: true)
                    return
                }

                // 兜底
                self.loadImageViaItemProvider(first)
            }
        } else {
            // 没有 assetIdentifier（首次权限或"选取照片"场景），直接走 itemProvider
            loadImageViaItemProvider(first)
        }
    }

    /// 使用 itemProvider 加载图片（保证 100% 成功）
    private func loadImageViaItemProvider(_ result: PHPickerResult) {
        if result.itemProvider.canLoadObject(ofClass: UIImage.self) {
            result.itemProvider.loadObject(ofClass: UIImage.self) { [weak self] object, error in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    self.hideLoading()
                    if let img = object as? UIImage {
                        self.showPreview(image: img, enableCrop: true)
                    } else {
                        self.showError(with: "camera_image_load_failed".localized)
                    }
                }
            }
        } else {
            self.hideLoading()
            self.showError(with: "camera_image_load_failed".localized)
        }
    }

    private func openEditor(with image: UIImage) {
        self.showPreview(image: image, enableCrop: true)
    }
        // MARK: - 通用图片编辑器
    private func presentImageEditor(for image: UIImage,
                                    completion: @escaping (UIImage) -> Void) {
        let lockScreenRatio = ZLImageClipRatio(title: "camera_lockscreen_ratio_title".localized, whRatio: 9.0 / 19.5)
        ZLImageEditorConfiguration.default()
            .editImageTools([.draw, .clip, .textSticker, .filter, .adjust, .imageSticker])
            .clipRatios([lockScreenRatio])
            .adjustTools([.brightness, .contrast, .saturation])
            .imageStickerContainerView(ImageStickerContainerView())

        ZLImageEditorUIConfiguration.default()
            .editDoneBtnBgColor(ACStyle.brandColor)
            .adjustSliderTintColor(ACStyle.brandColor)
            .ashbinTintBgColor(ACStyle.brandColor)
            .toolIconHighlightedColor(ACStyle.brandColor)
            .editDoneBtnTitleColor(.black)
        
        ZLEditImageViewController.showEditImageVC(
            parentVC: self,
            image: image
        ) { edited, _ in
            completion(edited)
        }
    }

    // cropping helpers are implemented in LockScreenCameraViewController+Crop.swift

    // MARK: - Crop Tip
    private func showCropTip() {
        cameraPreview.showGuideTip(text: "camera_crop_tip".localized, onceKey: "cropTip", duration: 2.0, position: .bottom(offset: 0))
    }

    /// 仅在拍摄模式 (.normal) 且不在预览状态时，刷新快门可用性
    private func updateShutterAvailability() {
        guard bottomBar.mode == .normal && !isPreviewing else { return }

        let enabled = captureSession.isRunning
        bottomBar.shutterButton.isEnabled = enabled
        bottomBar.outerBorder.alpha = enabled ? 1.0 : 0.3
    }

    // MARK: - Public API for History resend
    private(set) var isResendFromHistory: Bool = false
    
    private(set) var isResendFromUrl: String = ""

    /// Show preview for image selected from history (re-send). No crop.
    func previewForResend(image: UIImage,url: String) {
        isResendFromHistory = true
        isResendFromUrl = url
        showPreview(image: image, enableCrop: false)
    }

    @objc private func handleFlashToggle() {
        // Cycle: auto -> on -> off -> auto
        Haptic.impact(.light)
        switch currentFlashMode {
        case .auto:
            currentFlashMode = .on
            cameraPreview.showGuideTip(text: "camera_flash_on".localized, duration: 0.8, position: .center)
        case .on:
            currentFlashMode = .off
            cameraPreview.showGuideTip(text: "camera_flash_off".localized, duration: 0.8, position: .center)
        case .off:
            currentFlashMode = .auto
            cameraPreview.showGuideTip(text: "camera_flash_auto".localized, duration: 0.8, position: .center)
        @unknown default:
            currentFlashMode = .auto
        }
    }
}

extension LockScreenCameraViewController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput,
                     didFinishProcessingPhoto photo: AVCapturePhoto,
                     error: Error?) {
        if let data = photo.fileDataRepresentation(),
           var image = UIImage(data: data) {
            // 捕获的原图可能方向不是 .up，先做一次归正
            if image.imageOrientation != .up {
                UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
                image.draw(in: CGRect(origin: .zero, size: image.size))
                image = UIGraphicsGetImageFromCurrentImageContext() ?? image
                UIGraphicsEndImageContext()
            }

            // 裁剪到 9:19.5 中央区域，使之与相机 Preview 一致
            image = cropToLockRatio(image)

            showPreview(image: image, enableCrop: false)
        }
    }

    /// 将图片裁剪到 9:19.5 的中央区域
    private func cropToLockRatio(_ image: UIImage) -> UIImage {
        let targetRatio: CGFloat = 9.0 / 19.5 // 宽 / 高
        let size = image.size
        var cropRect: CGRect

        // 以图片坐标系（pixel）计算
        if size.width / size.height > targetRatio {
            // 图片太宽，以高度撑满
            let targetWidth = size.height * targetRatio
            let x = (size.width - targetWidth) / 2
            cropRect = CGRect(x: x, y: 0, width: targetWidth, height: size.height)
        } else {
            // 图片太高，以宽度撑满
            let targetHeight = size.width / targetRatio
            let y = (size.height - targetHeight) / 2
            cropRect = CGRect(x: 0, y: y, width: size.width, height: targetHeight)
        }

        guard let cg = image.cgImage?.cropping(to: cropRect) else { return image }
        return UIImage(cgImage: cg, scale: image.scale, orientation: .up)
    }
}

extension LockScreenCameraViewController {
    // MARK: - 自动化权限引导遮罩
    private func showAutomationOverlayIfNeeded() {
        // 如已完成或已存在遮罩则直接返回 / 移除
        if TutorialProgressManager.isCompleted(.automationDone) {
            automationOverlay?.removeFromSuperview()
            automationOverlay = nil
            bottomBar.isUserInteractionEnabled = true
            return
        }
        guard automationOverlay == nil else { return }

        let blurView = UIVisualEffectView(effect: UIBlurEffect(style: .dark))
        blurView.frame = cameraPreview.bounds
        blurView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        cameraPreview.addSubview(blurView)

        // 文案 + 按钮
        let titleLabel = UILabel()
        titleLabel.text = "camera_automation_title".localized
        titleLabel.textColor = .white
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.numberOfLines = 0
        titleLabel.textAlignment = .center

        let actionButton = UIButton()
        var actionButtoncfg = UIButton.Configuration.filled()
        actionButtoncfg.title = "camera_automation_go".localized
        actionButtoncfg.baseBackgroundColor = ACStyle.brandColor
        actionButtoncfg.baseForegroundColor = .black               // 标题颜色
        actionButtoncfg.cornerStyle = .medium
        actionButtoncfg.contentInsets = NSDirectionalEdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12)
        actionButtoncfg.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer { incoming in
            var outgoing = incoming
            outgoing.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
            return outgoing
        }
        actionButton.configuration = actionButtoncfg
        actionButton.addTarget(self, action: #selector(handleAutomationButton), for: .touchUpInside)
        
        // 忽略按钮
        let ignoreButton = UIButton()
        var ignoreButtoncfg = UIButton.Configuration.plain()
        ignoreButtoncfg.title = "camera_automation_done".localized
        ignoreButtoncfg.baseForegroundColor = .lightGray
        ignoreButtoncfg.contentInsets = NSDirectionalEdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12)
        ignoreButtoncfg.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer { incoming in
            var outgoing = incoming
            outgoing.font = UIFont.systemFont(ofSize: 13, weight: .regular)
            return outgoing
        }
        ignoreButton.configuration = ignoreButtoncfg
        ignoreButton.addTarget(self, action: #selector(handleIgnoreAutomation), for: .touchUpInside)
        
        let buttonsStack = UIStackView(arrangedSubviews: [actionButton, ignoreButton])
        buttonsStack.axis = .vertical
        buttonsStack.spacing = 6
        buttonsStack.alignment = .center
        
        let stack = UIStackView(arrangedSubviews: [titleLabel, buttonsStack])
        stack.axis = .vertical
        stack.spacing = 13
        stack.alignment = .center

        blurView.contentView.addSubview(stack)
        stack.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.greaterThanOrEqualToSuperview().offset(20)
            make.trailing.lessThanOrEqualToSuperview().offset(-20)
        }
        automationOverlay = blurView
        bottomBar.isUserInteractionEnabled = false // 禁用拍摄等按钮
    }

    @objc private func handleAutomationButton() {
        // 跳转到教程页面
        let vc = ShortcutGuideViewController()
        present(vc, animated: true)
    }
    
    @objc private func handleIgnoreAutomation() {
        // 移除遮罩，但不标记为完成，下次仍会提示
        TutorialProgressManager.setCompleted(.automationDone)
        automationOverlay?.removeFromSuperview()
        automationOverlay = nil
        bottomBar.isUserInteractionEnabled = true
    }

    @objc private func tutorialProgressDidChange() {
        // 教程进度变化时刷新遮罩
        DispatchQueue.main.async { [weak self] in
            self?.showAutomationOverlayIfNeeded()
            self?.showEmailBannerIfNeeded()
        }
    }

    /// 检查并决定是否显示邮箱横幅
    private func showEmailBannerIfNeeded() {
         
        // 条件：自动化完成 && 邮箱未绑定
        if TutorialProgressManager.isCompleted(.automationDone) &&
           !TutorialProgressManager.isCompleted(.emailBound) {

            // 已存在则无需重复创建
            guard emailBanner == nil else { return }

            // 容器
            let banner = UIView()
            banner.backgroundColor = .black
            banner.layer.cornerRadius = 20
            banner.layer.masksToBounds = true

            // 左侧图标
            let icon = UIImageView(image: UIImage(named: "main_float_lock"))
            icon.contentMode = .scaleAspectFill
            
            // 文案
            let titleLabel = UILabel()
            titleLabel.text = "camera_email_banner_title".localized
            titleLabel.textColor = .white
            titleLabel.numberOfLines = 0
            titleLabel.font = .systemFont(ofSize: 12, weight: .semibold)
            titleLabel.minimumScaleFactor = 0.7
            titleLabel.adjustsFontSizeToFitWidth = true

            let subtitleLabel = UILabel()
            subtitleLabel.text = "camera_email_banner_subtitle".localized
            subtitleLabel.textColor = UIColor.white.withAlphaComponent(0.6)
            subtitleLabel.font = .systemFont(ofSize: 10)
            subtitleLabel.numberOfLines = 0
            subtitleLabel.minimumScaleFactor = 0.7
            subtitleLabel.adjustsFontSizeToFitWidth = true
            
            let textStack = UIStackView(arrangedSubviews: [titleLabel, subtitleLabel])
            textStack.axis = .vertical
            textStack.spacing = 2
            textStack.alignment = .leading

            // 右侧 GO 按钮
            let goButton = UIButton(type: .custom)
            goButton.backgroundColor = ACStyle.brandColor
            goButton.setTitleColor(.black, for: .normal)
            goButton.setTitle("GO", for: .normal)
            goButton.titleLabel?.font = .systemFont(ofSize: 16,weight: .semibold)
            goButton.layer.cornerRadius = 15
            goButton.layer.masksToBounds = true
            goButton.addTarget(self, action: #selector(handleEmailBannerButton), for: .touchUpInside)

            // 横向 stack
            let hStack = UIStackView(arrangedSubviews: [icon, textStack, goButton])
            hStack.axis = .horizontal
            hStack.alignment = .center
            hStack.spacing = 6
            hStack.distribution = .fill
            banner.addSubview(hStack)
            hStack.snp.makeConstraints { make in
                make.edges.equalToSuperview().inset(6)
            }

            goButton.snp.makeConstraints { make in
                make.width.equalTo(45)
            }
            cameraPreview.addSubview(banner)
            banner.snp.makeConstraints { make in
                make.top.equalTo(cameraPreview).offset(9)
                make.centerX.equalToSuperview()
                make.leading.greaterThanOrEqualTo(cameraPreview).offset(9)
            }
  
            emailBanner = banner
        } else {
            // 不满足条件 → 隐藏横幅
            emailBanner?.removeFromSuperview()
            emailBanner = nil
        }
    }

    /// GO 按钮动作：跳转绑定邮箱页面
    @objc private func handleEmailBannerButton() {
        let vc = ShortcutGuideViewController()
        present(vc, animated: true)
    }
}
