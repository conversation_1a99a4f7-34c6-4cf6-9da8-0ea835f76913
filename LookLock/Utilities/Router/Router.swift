import UIKit
import AppIntents

enum Route {
    case intro
    case register
    case login
    case main
    case agreement(url: URL)
}

class Router {
    static let shared = Router()
    
    private init() {}
    
    func navigate(to route: Route, from viewController: UIViewController?, animated: Bool = true) {
        switch route {
        case .intro:
            let vc = IntroViewController()
            let nav = UINavigationController(rootViewController: vc)

            if let viewController = viewController {
                viewController.present(nav, animated: animated)
            } else {
                // 全局导航：直接设置为根视图控制器
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first {
                    UIView.transition(with: window, duration: 0.3, options: .transitionCrossDissolve, animations: {
                        window.rootViewController = nav
                    })
                }
            }
            
        case .register:
            let vc = RegisterEmailViewController()
            viewController?.navigationController?.pushViewController(vc, animated: animated)
            
        case .login:
            let vc = LoginViewController()
            viewController?.navigationController?.pushViewController(vc, animated: animated)

        case .main:
            let mainVC = ACMainContainerViewController()
            let navController = UINavigationController(rootViewController: mainVC)

            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                UIView.transition(with: window, duration: 0.3, options: .transitionCrossDissolve, animations: {
                    window.rootViewController = navController
                })
            }

        case .agreement(let url):
            let vc = AgreementWebViewController(url: url)
            viewController?.present(vc, animated: animated)
            
        }
    }
    
    static func runShortcutTapped() {
        // 生成运行快捷指令的URL
        let shortcutName = "LookLock"
        if let encodedName = shortcutName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
           let url = URL(string: "shortcuts://run-shortcut?name=\(encodedName)") {
            UIApplication.shared.open(url) { success in
                if !success {
                    // 如果无法打开快捷指令应用，可能是未安装，弹出提示
                    let alert = UIAlertController(
                        title: "无法打开快捷指令",
                        message: "请确保您已经在App Store安装了 快捷指令 应用",
                        preferredStyle: .alert
                    )
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    UIApplication.getTopViewController()?.present(alert, animated: true)
                }
            }
        }
    }
}
