//
//  Untitled.swift
//  LookLock
//
//  Created by ke wen on 4/27/25.
//

import UIKit
import SnapKit
import Kingfisher
import Combine
import MessageUI

// MARK: - Row Model
fileprivate enum SettingRow: Hashable {
    case guide
    case syncToLock(Bool)
    case setAsMain(Bool)
    case setLockWithShack(Bool)
    case keepOrientation(Bool)
    case feedback
    case privacy
    case logout

    // UI helpers
    var icon: String {
        switch self {
        case .guide:               return "set_openlock"
        case .syncToLock:          return "set_sync"
        case .setAsMain:           return "set_phone"
        case .keepOrientation:     return "set_camera"
        case .feedback:            return "set_feedback"
        case .privacy:             return "set_privacy"
        case .logout:              return "set_logout"
        case .setLockWithShack:    return "set_shake"

        }
    }

    var title: String {
        switch self {
        case .guide:               return "settings_guide".localized
        case .syncToLock:          return "settings_sync_to_lock".localized
        case .setAsMain:           return "settings_set_as_main".localized
        case .keepOrientation:     return "settings_keep_orientation".localized
        case .feedback:            return "settings_feedback".localized
        case .privacy:             return "settings_privacy".localized
        case .logout:              return "settings_logout".localized
        case .setLockWithShack:
            return "settings_lock_with_shake".localized
        }
    }

    // Toggle helpers
    var isToggle: Bool {
        switch self {
        case .syncToLock, .setAsMain, .keepOrientation, .setLockWithShack:
            return true
        default:
            return false
        }
    }

    var toggleState: Bool {
        switch self {
        case .syncToLock(let v), .setAsMain(let v), .keepOrientation(let v), .setLockWithShack(let v):
            return v
        default:
            return false
        }
    }

    func updatingToggle(_ newValue: Bool) -> SettingRow {
        switch self {
        case .syncToLock:      return .syncToLock(newValue)
        case .setAsMain:       return .setAsMain(newValue)
        case .keepOrientation: return .keepOrientation(newValue)
        case .setLockWithShack: return .setLockWithShack(newValue)
        default:               return self
        }
    }
}

// MARK: - Cell Type Enum
fileprivate enum SettingCellType {
    case normal
    case toggle(Bool)
}

// MARK: - SettingsViewController
class SettingsViewController: BaseViewController {

    // UI
    private let avatarImageView = UIImageView()
    private let editIcon = UIImageView(image: UIImage(named: "set_avatar_edit"))
    private let nicknameLabel = UILabel()
    private let tableView = UITableView(frame: .zero, style: .plain)
    private let logoutButton = UIButton(type: .system)
    private let backButton = UIButton(type: .system)

    // Data
    private var settings: [SettingRow] = []
    private var cancellables = Set<AnyCancellable>()

    // 控制是否显示"同时设为主屏幕"
    var showSetAsMainScreenToggle: Bool = false {
        didSet { rebuildSettings() }
    }

    // 若在容器中展示，用于返回相机页
    weak var containerViewController: ACMainContainerViewController?

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        rebuildSettings()
        bind()
    }

    override func setupLocalization() {
        // 重新构建设置项以更新本地化文本
        rebuildSettings()
        // 更新注销按钮文本
        logoutButton.setTitle("settings_deregister".localized, for: .normal)
        // 刷新表格视图
        tableView.reloadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    // MARK: - Data Construction
    private func rebuildSettings() {
        let us = UserService.shared.userSettings
        var rows: [SettingRow] = [
            .guide,
            .syncToLock(us.syncToLockScreen)
        ]
        if showSetAsMainScreenToggle {
            rows.append(.setAsMain(us.setAsMainScreen))
        }
        rows.append(contentsOf: [
            .keepOrientation(us.keepLastCameraOrientation),
            .setLockWithShack(us.setLockWithShack),
            .feedback,
            .privacy,
            .logout
        ])
        settings = rows
        tableView.reloadData()
        tableView.snp.updateConstraints { make in
            make.height.equalTo(rows.count * 54)
        }
    }

    // MARK: - UI
    override func setupUI() {
        view.backgroundColor = .black

        // Nav bar
        let navBar = UIView()
        view.addSubview(navBar)
        navBar.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(SafeAreaManager.shared.statusBarHeight)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        backButton.setImage(UIImage(named: "main_back"), for: .normal)
        backButton.tintColor = .white
        backButton.addTarget(self, action: #selector(backTapped), for: .touchUpInside)
        navBar.addSubview(backButton)
        backButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        // Avatar
        avatarImageView.layer.cornerRadius = 42
        avatarImageView.layer.borderWidth = 2
        avatarImageView.layer.borderColor = UIColor.darkGray.cgColor
        avatarImageView.clipsToBounds = true
        avatarImageView.isUserInteractionEnabled = true
        avatarImageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(editProfile)))
        view.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.top.equalTo(navBar.snp.bottom)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(84)
        }

        // Edit icon
        editIcon.isUserInteractionEnabled = true
        editIcon.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(editProfile)))
        view.addSubview(editIcon)
        editIcon.snp.makeConstraints { make in
            make.bottom.right.equalTo(avatarImageView).inset(2)
            make.width.height.equalTo(18)
        }

        // Nickname
        nicknameLabel.textColor = .white
        nicknameLabel.font = .systemFont(ofSize: 16, weight: .medium)
        nicknameLabel.textAlignment = .center
        nicknameLabel.adjustsFontSizeToFitWidth = true
        nicknameLabel.minimumScaleFactor = 0.7
        nicknameLabel.numberOfLines = 1
        nicknameLabel.isUserInteractionEnabled = true
        nicknameLabel.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(editProfile)))
        view.addSubview(nicknameLabel)
        nicknameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(6)
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
        }

        // TableView
        tableView.backgroundColor = UIColor(hex: "#141414")
        tableView.layer.cornerRadius = 24
        tableView.separatorStyle = .none
        tableView.isScrollEnabled = false
        tableView.dataSource = self
        tableView.delegate = self
        tableView.register(SettingCell.self, forCellReuseIdentifier: "SettingCell")
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(nicknameLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(settings.count * 54)
        }

        // Deregister button
        logoutButton.setTitle("settings_deregister".localized, for: .normal)
        logoutButton.setTitleColor(.gray, for: .normal)
        logoutButton.titleLabel?.font = .systemFont(ofSize: 12)
        logoutButton.addTarget(self, action: #selector(deleteAccountTapped), for: .touchUpInside)
        view.addSubview(logoutButton)
        logoutButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(SafeAreaManager.shared.bottomSafeAreaInset)
        }
    }

    // MARK: - Bindings
    private func bind() {
        UserService.shared.currentUserPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] in self?.updateUserInfo($0) }
            .store(in: &cancellables)

        UserService.shared.userSettingsPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in self?.rebuildSettings() }
            .store(in: &cancellables)
    }

    private func updateUserInfo(_ profile: UserProfile?) {
        guard let p = profile else { return }
        if let url = URL(string: p.avatar) {
            avatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "avatar_placeholder"))
        }
        nicknameLabel.text = p.nickname
    }

    // MARK: - Actions
    @objc private func backTapped() {
        containerViewController?.showCamera()
    }

    @objc private func editProfile() {
        present(EditProfileViewController(), animated: true)
    }

    @objc private func deleteAccountTapped() {
        let alert = ACAlertViewController(
            title: "settings_deregister_confirm_title".localized,
            message: "settings_deregister_confirm_message".localized,
            actions: [
                ACAlertAction(title: "cancel".localized, style: .secondary),
                ACAlertAction(title: "settings_deregister_confirm_button".localized, style: .primary) { [weak self] in
                    self?.performDeregistration()
                }
            ])
        present(alert, animated: true)
    }

    private func performDeregistration() {
        backButton.isEnabled = false
        logoutButton.isEnabled = false
        showLoading(with: "settings_deregistering".localized)
        Task { [weak self] in
            do {
                try await UserService.shared.deregisterAccount()
                self?.hideLoading()
                UIApplication.shared.setRootViewController(UINavigationController(rootViewController: IntroViewController()), animated: true)
            } catch {
                self?.hideLoading()
                self?.showError(with: error.localizedDescription)
                self?.backButton.isEnabled = true
                self?.logoutButton.isEnabled = true
            }
        }
    }

    deinit { cancellables.removeAll() }
}

// MARK: - UITableView
extension SettingsViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        settings.count
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat { 54 }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath) as! SettingCell
        let row = settings[indexPath.row]
        let cellType: SettingCellType = row.isToggle ? .toggle(row.toggleState) : .normal
        cell.configure(icon: row.icon, title: row.title, type: cellType)
        cell.selectionStyle = .none
        cell.onToggleChanged = { [weak self] isOn in
            self?.handleToggle(for: row, newValue: isOn)
        }
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        switch settings[indexPath.row] {
        case .guide:          showLockScreenGuide()
        case .feedback:       showFeedback()
        case .privacy:        showPrivacyPolicy()
        case .logout:         handleLogout()
        default: break
        }
    }

    // MARK: Toggle Handling
    private func handleToggle(for row: SettingRow, newValue: Bool) {
        var us = UserService.shared.userSettings
        switch row {
        case .syncToLock:
            us.syncToLockScreen = newValue
            us.syncPreference = false
        case .setLockWithShack: us.setLockWithShack = newValue
        case .setAsMain:         us.setAsMainScreen = newValue
        case .keepOrientation:   us.keepLastCameraOrientation = newValue
        default: return
        }
        Task { try? await UserService.shared.updateUserSettings(us) }
    }
}

// MARK: - Cell
fileprivate class SettingCell: UITableViewCell {
    private let iconView = UIImageView()
    private let titleLabel = UILabel()
    private let toggleSwitch = UISwitch()
    var onToggleChanged: ((Bool) -> Void)?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        backgroundColor = .clear
        setup()
    }
    required init?(coder: NSCoder) { fatalError() }

    private func setup() {
        iconView.tintColor = .white
        contentView.addSubview(iconView)
        iconView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(14)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(26)
        }
        titleLabel.textColor = .white
        titleLabel.font = .systemFont(ofSize: 14)
        titleLabel.adjustsFontSizeToFitWidth = true
        titleLabel.minimumScaleFactor = 0.8
        titleLabel.numberOfLines = 1
        contentView.addSubview(titleLabel)
        // 初始约束，会在 configure 方法中根据类型重新设置
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconView.snp.right).offset(6)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualToSuperview().offset(-14)
        }
        toggleSwitch.thumbTintColor = .black
        toggleSwitch.onTintColor = ACStyle.brandColor
        toggleSwitch.addTarget(self, action: #selector(switchChanged), for: .valueChanged)
        contentView.addSubview(toggleSwitch)
        toggleSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-14)
            make.centerY.equalToSuperview()
        }
    }

    func configure(icon: String, title: String, type: SettingCellType) {
        iconView.image = UIImage(named: icon)
        titleLabel.text = title

        // 更新标题标签的右边约束，根据是否有开关来调整
        titleLabel.snp.remakeConstraints { make in
            make.left.equalTo(iconView.snp.right).offset(6)
            make.centerY.equalToSuperview()
            switch type {
            case .normal:
                make.right.lessThanOrEqualToSuperview().offset(-14)
            case .toggle:
                make.right.lessThanOrEqualTo(toggleSwitch.snp.left).offset(-8)
            }
        }

        switch type {
        case .normal:
            toggleSwitch.isHidden = true
        case .toggle(let val):
            toggleSwitch.isHidden = false
            toggleSwitch.isOn = val
        }
    }

    @objc private func switchChanged() {
        onToggleChanged?(toggleSwitch.isOn)
    }
}



// MARK: - Row Actions
private extension SettingsViewController {
    func showLockScreenGuide() { present(ShortcutGuideViewController(), animated: true) }
    func showFeedback() {
        // Get the current user ID
        let userId = UserService.shared.currentUser?.userId ?? ""

        // Construct the feedback URL with user ID
        let feedbackURLString = "https://www.test-looklock.actiontech.online/h5/feedback?user_id=\(userId)"

        guard let feedbackURL = URL(string: feedbackURLString) else {
            let alert = UIAlertController(title: "settings_feedback_error_title".localized, message: "settings_feedback_error_message".localized, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "ok".localized, style: .default))
            present(alert, animated: true)
            return
        }

        // Open the feedback URL in Safari
        Router.shared.navigate(to: .agreement(url: feedbackURL), from: self)
    }
    func showPrivacyPolicy() {
        if let url = URL(string: "https://www.freeprivacypolicy.com/live/************************************") {
            Router.shared.navigate(to: .agreement(url: url), from: self)
        }
    }
    func handleLogout() {
        let cancel = ACAlertAction(title: "cancel".localized, style: .secondary)
        let confirm = ACAlertAction(title: "settings_logout".localized, style: .primary) {
            Task {
                try? await UserService.shared.logout()
                await MainActor.run {
                    Router.shared.navigate(to: .intro, from: nil)
                }
            }
        }

        let alertVC = ACAlertViewController(
            title: "settings_logout_confirm_title".localized,
            message: nil,
            actions: [cancel, confirm]
        )
        present(alertVC, animated: true)
    }
}
