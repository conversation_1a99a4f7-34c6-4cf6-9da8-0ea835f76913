//
//  File.swift
//  LookLock
//
//  Created by ke wen on 8/5/25.
//


// MARK: - 便利扩展

extension SessionExpiredPlugin {
    
    /// 显示会话过期提示并执行登出的默认处理逻辑
    public static func defaultSessionExpiredHandler() {
        print("🔐 检测到会话过期 (错误代码 10001)，开始登出流程...")

        DispatchQueue.main.async {
            // 显示本地化错误提示
            if let topVC = UIApplication.shared.topViewController {
                let alert = UIAlertController(
                    title: "alert_title_error".localized,
                    message: "error_unauthorized".localized,
                    preferredStyle: .alert
                )

                alert.addAction(UIAlertAction(title: "ok".localized, style: .default) { _ in
                    // 用户确认后执行登出
                    Task {
                        try? await UserService.shared.logout()
                        await MainActor.run {
                            Router.shared.navigate(to: .intro, from: nil)
                        }
                    }
                })

                topVC.present(alert, animated: true)
            } else {
                // 无法显示弹窗，直接执行登出
                Task {
                    try? await UserService.shared.logout()
                    await MainActor.run {
                        Router.shared.navigate(to: .intro, from: nil)
                    }
                }
            }
        }
    }
}
