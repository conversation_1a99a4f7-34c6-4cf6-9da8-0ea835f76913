import Foundation
import Moya
import UIKit

/// 自定义 Moya 插件，用于统一处理 API 响应中 status == 10001 的会话过期情况
public final class SessionExpiredPlugin: PluginType {
    
    /// 会话过期处理闭包类型
    public typealias SessionExpiredHandler = () -> Void
    
    /// 会话过期处理闭包
    private let sessionExpiredHandler: SessionExpiredHandler
    
    /// 初始化插件
    /// - Parameter sessionExpiredHandler: 会话过期时的处理闭包
    public init(sessionExpiredHandler: @escaping SessionExpiredHandler) {
        self.sessionExpiredHandler = sessionExpiredHandler
    }
    
    /// 处理响应结果，检查是否存在会话过期状态
    /// - Parameters:
    ///   - result: Moya 响应结果
    ///   - target: 请求目标
    /// - Returns: 处理后的响应结果
    public func process(_ result: Result<Moya.Response, MoyaError>, target: TargetType) -> Result<Moya.Response, MoyaError> {
        switch result {
        case .success(let response):
            // 检查响应中是否包含会话过期状态码
            if isSessionExpired(response: response) {
                print("🔐 SessionExpiredPlugin: 检测到会话过期 (status: 10001) - \(target.path)")
                
                // 在主线程执行会话过期处理
                DispatchQueue.main.async { [weak self] in
                    self?.sessionExpiredHandler()
                }
                
                // 返回自定义的会话过期错误，阻止后续处理
                let sessionExpiredError = MoyaError.underlying(
                    SessionExpiredError.sessionExpired,
                    response
                )
                return .failure(sessionExpiredError)
            }
            
            return .success(response)
            
        case .failure(let error):
            // 对于网络错误，直接返回原始错误
            return .failure(error)
        }
    }
    
    /// 检查响应是否包含会话过期状态码
    /// - Parameter response: Moya 响应对象
    /// - Returns: 是否为会话过期
    private func isSessionExpired(response: Moya.Response) -> Bool {
        do {
            // 尝试解析响应为通用 API 响应格式
            let apiResponse = try JSONDecoder().decode(APIStatusResponse.self, from: response.data)
            return apiResponse.status == 10001
        } catch {
            // 解析失败，不是标准 API 响应格式，不处理
            return false
        }
    }
}

// MARK: - 辅助类型

/// 会话过期错误类型
public enum SessionExpiredError: Error, LocalizedError {
    case sessionExpired
    
    public var errorDescription: String? {
        switch self {
        case .sessionExpired:
            return "error_unauthorized".localized
        }
    }
}

/// 用于解析 API 响应状态的轻量级模型
private struct APIStatusResponse: Decodable {
    let status: Int
    let message: String?
}

// MARK: - 默认会话过期处理器

extension SessionExpiredPlugin {
    
    /// 创建带有默认会话过期处理逻辑的插件实例
    /// - Returns: 配置好的 SessionExpiredPlugin 实例
    public static func createDefault() -> SessionExpiredPlugin {
        return SessionExpiredPlugin {
            SessionExpiredPlugin.defaultSessionExpiredHandler()
        }
    }
    
    /// 创建带有自定义处理逻辑的插件实例
    /// - Parameter customHandler: 自定义的会话过期处理逻辑
    /// - Returns: 配置好的 SessionExpiredPlugin 实例
    public static func create(with customHandler: @escaping SessionExpiredHandler) -> SessionExpiredPlugin {
        return SessionExpiredPlugin(sessionExpiredHandler: customHandler)
    }
}

