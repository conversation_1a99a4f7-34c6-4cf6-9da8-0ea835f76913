# Moya 插件使用指南

## SessionExpiredPlugin - 会话过期处理插件

### 概述

`SessionExpiredPlugin` 是一个自定义的 Moya 插件，用于统一处理 API 响应中 `status == 10001` 的会话过期情况。通过使用这个插件，可以避免在每个 API 调用处重复编写会话过期处理逻辑。

### 功能特性

- ✅ 自动检测 API 响应中的 `status == 10001` 状态码
- ✅ 统一处理会话过期逻辑，避免代码重复
- ✅ 支持自定义会话过期处理逻辑
- ✅ 提供默认的会话过期处理实现
- ✅ 线程安全，自动在主线程执行 UI 相关操作
- ✅ 与现有 NetworkManager 架构无缝集成

### 使用方式

#### 1. 基本集成

在 `NetworkManager` 中集成插件：

```swift
private init() {
    let configuration = URLSessionConfiguration.default
    configuration.timeoutIntervalForRequest = NetworkConfig.timeoutInterval
    configuration.timeoutIntervalForResource = NetworkConfig.timeoutInterval
    
    let logger = NetworkLogger()
    let sessionExpiredPlugin = SessionExpiredPlugin.createDefault()
    
    provider = MoyaProvider<APIService>(
        session: Session(configuration: configuration),
        plugins: [logger, sessionExpiredPlugin]
    )
}
```

#### 2. 使用默认处理逻辑

```swift
// 创建使用默认会话过期处理逻辑的插件
let plugin = SessionExpiredPlugin.createDefault()
```

默认处理逻辑包括：
- 显示本地化的错误提示弹窗
- 用户确认后执行登出操作
- 导航到登录页面
- 如果无法显示弹窗，直接执行登出

#### 3. 使用自定义处理逻辑

```swift
// 创建使用自定义处理逻辑的插件
let plugin = SessionExpiredPlugin.create { 
    // 自定义的会话过期处理逻辑
    print("会话已过期，执行自定义处理")
    
    // 例如：显示自定义提示
    DispatchQueue.main.async {
        // 自定义 UI 处理
    }
}
```

#### 4. 直接使用静态方法

```swift
// 也可以直接调用默认处理逻辑
SessionExpiredPlugin.defaultSessionExpiredHandler()
```

### 工作原理

1. **响应拦截**: 插件在 `process` 方法中拦截所有 API 响应
2. **状态检查**: 尝试解析响应 JSON，检查 `status` 字段是否等于 10001
3. **错误处理**: 如果检测到会话过期，执行预定义的处理逻辑
4. **错误转换**: 将会话过期响应转换为 `MoyaError`，阻止后续处理

### 错误类型

插件定义了专门的错误类型：

```swift
public enum SessionExpiredError: Error, LocalizedError {
    case sessionExpired
    
    public var errorDescription: String? {
        return "error_unauthorized".localized
    }
}
```

### 与现有代码的兼容性

- ✅ 完全向后兼容，不影响现有 API 调用
- ✅ 自动处理会话过期，无需修改现有业务逻辑
- ✅ 移除了 NetworkManager 中的重复处理代码
- ✅ 保持了原有的错误处理机制

### 最佳实践

1. **统一配置**: 在 NetworkManager 初始化时配置插件，确保所有 API 请求都受到保护
2. **错误处理**: 在业务层可以通过检查 `SessionExpiredError` 来识别会话过期错误
3. **测试**: 可以通过模拟 `status: 10001` 的响应来测试插件功能
4. **日志记录**: 插件会自动记录会话过期事件，便于调试和监控

### 注意事项

- 插件只处理能够成功解析为 JSON 且包含 `status` 字段的响应
- 会话过期处理逻辑会在主线程执行，确保 UI 操作的安全性
- 插件会将会话过期响应转换为错误，业务层的 completion 回调不会收到成功响应

### 扩展性

如果需要处理其他状态码或添加更多功能，可以：

1. 扩展 `SessionExpiredPlugin` 类
2. 创建新的插件类继承 `PluginType`
3. 在 `APIStatusResponse` 中添加更多字段
4. 自定义错误类型和处理逻辑
