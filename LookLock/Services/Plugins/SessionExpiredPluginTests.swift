import XCTest
import Moya
@testable import LookLock

class SessionExpiredPluginTests: XCTestCase {
    
    var plugin: SessionExpiredPlugin!
    var mockTarget: MockAPITarget!
    var sessionExpiredHandlerCalled: Bool = false
    
    override func setUp() {
        super.setUp()
        sessionExpiredHandlerCalled = false
        mockTarget = MockAPITarget()
        
        // 创建带有测试处理器的插件
        plugin = SessionExpiredPlugin { [weak self] in
            self?.sessionExpiredHandlerCalled = true
        }
    }
    
    override func tearDown() {
        plugin = nil
        mockTarget = nil
        sessionExpiredHandlerCalled = false
        super.tearDown()
    }
    
    // MARK: - 测试会话过期检测
    
    func testSessionExpiredDetection() {
        // 创建包含 status: 10001 的响应数据
        let responseData = """
        {
            "status": 10001,
            "message": "Session expired",
            "data": null
        }
        """.data(using: .utf8)!
        
        let response = Response(statusCode: 200, data: responseData)
        let result = Result<Response, MoyaError>.success(response)
        
        // 处理响应
        let processedResult = plugin.process(result, target: mockTarget)
        
        // 验证结果
        switch processedResult {
        case .success:
            XCTFail("应该返回失败结果")
        case .failure(let error):
            // 验证错误类型
            if case .underlying(let underlyingError, _) = error,
               underlyingError is SessionExpiredError {
                XCTAssertTrue(true, "正确检测到会话过期错误")
            } else {
                XCTFail("错误类型不正确: \(error)")
            }
        }
        
        // 验证处理器被调用
        // 由于处理器在主线程异步执行，需要等待
        let expectation = XCTestExpectation(description: "Session expired handler called")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            XCTAssertTrue(self.sessionExpiredHandlerCalled, "会话过期处理器应该被调用")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 测试正常响应
    
    func testNormalResponsePassThrough() {
        // 创建正常响应数据
        let responseData = """
        {
            "status": 200,
            "message": "Success",
            "data": {"key": "value"}
        }
        """.data(using: .utf8)!
        
        let response = Response(statusCode: 200, data: responseData)
        let result = Result<Response, MoyaError>.success(response)
        
        // 处理响应
        let processedResult = plugin.process(result, target: mockTarget)
        
        // 验证结果
        switch processedResult {
        case .success(let processedResponse):
            XCTAssertEqual(processedResponse.statusCode, 200)
            XCTAssertEqual(processedResponse.data, responseData)
        case .failure:
            XCTFail("正常响应不应该失败")
        }
        
        // 验证处理器未被调用
        XCTAssertFalse(sessionExpiredHandlerCalled, "正常响应不应该触发会话过期处理器")
    }
    
    // MARK: - 测试其他错误状态码
    
    func testOtherErrorStatusCodes() {
        // 测试其他错误状态码（如 400, 500 等）
        let errorCodes = [400, 401, 403, 500, 502]
        
        for errorCode in errorCodes {
            let responseData = """
            {
                "status": \(errorCode),
                "message": "Error \(errorCode)",
                "data": null
            }
            """.data(using: .utf8)!
            
            let response = Response(statusCode: 200, data: responseData)
            let result = Result<Response, MoyaError>.success(response)
            
            // 处理响应
            let processedResult = plugin.process(result, target: mockTarget)
            
            // 验证结果 - 其他错误状态码应该正常通过
            switch processedResult {
            case .success:
                XCTAssertTrue(true, "错误状态码 \(errorCode) 应该正常通过")
            case .failure:
                XCTFail("错误状态码 \(errorCode) 不应该被插件拦截")
            }
        }
        
        // 验证处理器未被调用
        XCTAssertFalse(sessionExpiredHandlerCalled, "其他错误状态码不应该触发会话过期处理器")
    }
    
    // MARK: - 测试无效 JSON 响应
    
    func testInvalidJSONResponse() {
        // 创建无效的 JSON 响应
        let responseData = "Invalid JSON".data(using: .utf8)!
        
        let response = Response(statusCode: 200, data: responseData)
        let result = Result<Response, MoyaError>.success(response)
        
        // 处理响应
        let processedResult = plugin.process(result, target: mockTarget)
        
        // 验证结果 - 无效 JSON 应该正常通过
        switch processedResult {
        case .success:
            XCTAssertTrue(true, "无效 JSON 应该正常通过")
        case .failure:
            XCTFail("无效 JSON 不应该被插件拦截")
        }
        
        // 验证处理器未被调用
        XCTAssertFalse(sessionExpiredHandlerCalled, "无效 JSON 不应该触发会话过期处理器")
    }
    
    // MARK: - 测试网络错误
    
    func testNetworkError() {
        // 创建网络错误
        let networkError = NSError(domain: "TestDomain", code: -1, userInfo: nil)
        let result = Result<Response, MoyaError>.failure(.underlying(networkError, nil))
        
        // 处理响应
        let processedResult = plugin.process(result, target: mockTarget)
        
        // 验证结果 - 网络错误应该直接返回
        switch processedResult {
        case .success:
            XCTFail("网络错误应该返回失败结果")
        case .failure(let error):
            if case .underlying(let underlyingError, _) = error,
               (underlyingError as NSError).domain == "TestDomain" {
                XCTAssertTrue(true, "网络错误应该直接返回")
            } else {
                XCTFail("网络错误类型不正确")
            }
        }
        
        // 验证处理器未被调用
        XCTAssertFalse(sessionExpiredHandlerCalled, "网络错误不应该触发会话过期处理器")
    }
    
    // MARK: - 测试默认插件创建
    
    func testDefaultPluginCreation() {
        let defaultPlugin = SessionExpiredPlugin.createDefault()
        XCTAssertNotNil(defaultPlugin, "应该能够创建默认插件")
    }
    
    // MARK: - 测试自定义插件创建
    
    func testCustomPluginCreation() {
        var customHandlerCalled = false
        let customPlugin = SessionExpiredPlugin.create {
            customHandlerCalled = true
        }
        
        XCTAssertNotNil(customPlugin, "应该能够创建自定义插件")
        
        // 测试自定义处理器
        let responseData = """
        {
            "status": 10001,
            "message": "Session expired",
            "data": null
        }
        """.data(using: .utf8)!
        
        let response = Response(statusCode: 200, data: responseData)
        let result = Result<Response, MoyaError>.success(response)
        
        _ = customPlugin.process(result, target: mockTarget)
        
        // 验证自定义处理器被调用
        let expectation = XCTestExpectation(description: "Custom handler called")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            XCTAssertTrue(customHandlerCalled, "自定义处理器应该被调用")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
}

// MARK: - Mock 类型

class MockAPITarget: TargetType {
    var baseURL: URL { URL(string: "https://api.example.com")! }
    var path: String { "/test" }
    var method: Moya.Method { .get }
    var task: Task { .requestPlain }
    var headers: [String: String]? { nil }
}
