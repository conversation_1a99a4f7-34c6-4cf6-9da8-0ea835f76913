// Common
"ok" = "确定";
"cancel" = "取消";
"save" = "保存";
"delete" = "删除";
"edit" = "编辑";
"back" = "返回";
"continue" = "继续";

// Error Messages
"error_network" = "网络连接错误";
"error_server" = "服务器错误";
"error_unknown" = "未知错误";
"error_invalid_email" = "请输入有效的邮箱地址";
"error_password_length" = "密码长度至少为8位";
"error_wrong_password" = "密码错误";
"error_user_not_found" = "用户不存在";
"error_user_disabled" = "账户已被禁用";
"error_too_many_requests" = "请求过于频繁，请稍后重试";
"error_email_already_in_use" = "邮箱已被使用";
"error_weak_password" = "密码强度不够";
"error_operation_not_allowed" = "操作不被允许";
"error_unauthorized" = "登录已过期，请重新登录";

// Loading
"loading" = "加载中...";
"loading_failed" = "加载失败";

// Alert
"alert_title_error" = "错误";
"alert_title_warning" = "警告";
"alert_title_success" = "成功";

// Login
"login_title" = "登录";
"login_username" = "用户名";
"login_password" = "密码";
"login_button" = "登录";
"login_forgot_password" = "忘记密码？";
"login_register" = "注册账号";
"login_email_title" = "您的邮箱是什么？";
"login_password_title" = "请输入您的密码";
"login_email_placeholder" = "输入您的邮箱";
"login_password_placeholder" = "输入您的密码";
"login_continue" = "继续";
"login_forgot_password_button" = "忘记密码？";
"login_password_hint" = "您的密码必须至少8个字符";
"login_error_invalid_email" = "无效的邮箱地址。";
"login_error_password_length" = "密码必须至少8个字符。";
"login_forgot_password_title" = "提示";
"login_forgot_password_message" = "请先输入有效邮箱";
"login_reset_email_sent_title" = "邮件已发送";
"login_reset_email_sent_message" = "我们已向您的邮箱发送重置链接，请查收。";
"login_reset_email_sent_title_en" = "邮件已发送";
"login_reset_email_sent_message_en" = "密码重置邮件已发送。请检查您的收件箱。";
"login_terms_service" = "服务条款";
"login_privacy_policy" = "隐私政策";

// Removed duplicate Register keys - consolidated in Register section below

// Avatar
"avatar_uploading" = "头像上传中...";
"avatar_upload_success" = "头像上传成功";
"avatar_upload_failed" = "头像上传失败";
"avatar_select" = "选择头像";
"avatar_take_photo" = "拍照";
"avatar_choose_library" = "从相册选择";

// Logout
"logout_button" = "退出登录";
"logout_confirm_title" = "确认退出";
"logout_confirm_message" = "确定要退出登录吗？";

// Avatar Upload
"error_invalid_image" = "无效的图片";
"error_invalid_response" = "服务器返回数据无效"; 


/* 第三方登录错误信息 */
"third_party_auth_user_cancelled" = "用户取消登录";
"third_party_auth_network_error" = "网络连接失败";
"third_party_auth_invalid_credentials" = "登录凭证无效";
"third_party_auth_service_unavailable" = "登录服务暂时不可用";
"third_party_auth_configuration_error" = "登录配置错误";
"third_party_auth_unknown_error" = "未知错误";

/* 第三方登录按钮文本 */
"continue_with_apple" = "Continue with Apple";
"continue_with_google" = "Continue with Google";
"continue_with_wechat" = "使用微信登录";
"continue_with_qq" = "使用QQ登录";

/* 登录状态 */
"connecting" = "连接中...";
"login_failed" = "登录失败";
"login_success" = "登录成功";

/* Removed duplicate error keys - already defined in Error Messages section above */

/* Removed duplicate keys - already defined in Common section above */
// Legacy keys for backward compatibility
"Terms" = "服务条款";
"Privacy Policy" = "隐私政策";
"By tapping Continue, you agree to our Terms and Privacy Policy." = "点击继续即表示您同意我们的服务条款和隐私政策。";

// Settings
"settings_guide" = "如何开启锁屏投送";
"settings_sync_to_lock" = "同步为我的锁屏";
"settings_set_as_main" = "同时设为主屏幕";
"settings_keep_orientation" = "保留上次使用镜头方向";
"settings_feedback" = "意见反馈";
"settings_privacy" = "隐私政策";
"settings_logout" = "退出登录";
"settings_lock_with_shake" = "锁屏更改时震动提醒";
"settings_deregister" = "注销账号";
"settings_deregister_confirm_title" = "确定要注销账号吗？";
"settings_deregister_confirm_message" = "注销后，您的账号将被永久删除，所有数据将无法恢复。";
"settings_deregister_confirm_button" = "确定注销";
"settings_deregistering" = "正在注销…";
"settings_logout_confirm_title" = "确认退出登录";
"settings_feedback_error_title" = "错误";
"settings_feedback_error_message" = "无法打开反馈页面";

// Shortcut Guide
"shortcut_guide_greeting" = "Hi, %@";
"shortcut_guide_steps" = "3 步快速启动";
"shortcut_guide_prev" = "上一步";
"shortcut_guide_next" = "下一步";
"shortcut_guide_start" = "立即开始";
"shortcut_guide_complete" = "完成";

// Intro
"intro_title" = "LookLock";
"intro_slogan" = "随时霸占好友屏幕";
"intro_agreement_text" = "点击登录并使用 LookLock，即表示您同意我们的服务条款和隐私政策。";
"intro_terms_service" = "服务条款";
"intro_privacy_policy" = "隐私政策";
"intro_login_failed_title" = "登录失败";
"intro_login_failed_ok" = "确定";

// Lock Intent
"lock_intent_unauthorized" = "未登录或认证已过期，请先登录应用";
"lock_intent_network_error" = "网络请求失败，请检查网络连接";
"lock_intent_no_image_url" = "未找到可用的锁屏图片";
"lock_intent_version_low" = "快捷指令版本过低,请前往APP升级";
"lock_intent_first_init" = "👀为了更好的体验，请允许所有快件指令权限";

// Image Upload Errors
"image_upload_invalid" = "图片无效或处理失败";
"image_upload_failed" = "上传失败: %@";
"image_upload_token_failed" = "获取上传凭证失败: %@";
"image_upload_invalid_response" = "服务器响应异常";
"image_upload_user_not_found" = "用户未登录，请重新登录后重试";

// Friends
"friends_title" = "好友列表";
"friends_pull_to_refresh" = "下拉刷新";
"friends_empty_message" = "暂无好友，快去添加吧～";
"friends_empty_button" = "添加新朋友";
"friends_delete_confirm_title" = "确认删除该好友?";
"friends_delete_confirm_message" = "删除后将无法投送照片至对方锁屏";
"friends_delete_confirm_button" = "确认";

// Removed duplicate Add Friend keys - consolidated in Add Friend View Controller section below

// Popup
"popup_friend_request_success" = "已成功添加好友";
"popup_friend_request_failed" = "添加失败，请重试";
"popup_operation_failed" = "操作失败，请重试";

// Removed duplicate Camera keys - consolidated in Lock Screen Camera section below

// Register
"register_email_title" = "您的邮箱是什么？";
"register_email_placeholder" = "输入您的邮箱";
"register_password_title" = "设置您的密码";
"register_password_placeholder" = "设置您的密码";
"register_password_hint" = "您的密码必须至少8个字符";
"register_continue" = "继续";
"register_button" = "注册";
"register_error_invalid_email" = "无效的邮箱地址。";
"register_error_password_length" = "密码必须至少8个字符。";
"register_email" = "邮箱登录";
// Photo Preview
"photo_preview_send_to_friend" = "投送好友";
"photo_preview_set_as_lockscreen" = "设为锁屏";
"photo_preview_dont_ask_again" = "下次不再询问";
"photo_preview_ok" = "好的";
"photo_preview_lockscreen_tip" = "如果设置锁屏失败是因为上一锁屏霸占时间过短，再次设定就行啦";

// History
"history_empty_message" = "暂无历史记录";
"history_load_more_failed" = "加载更多失败，请下拉刷新重试";

// Friend Selection
"friend_selection_title" = "谁可以看到";
"friend_selection_confirm" = "确认";
"friend_selection_all_friends" = "所有好友可见";
"friend_selection_specific_friends" = "部分好友可见";

// Friend Add Success
"friend_add_success_later" = "下次再说";
"friend_add_success_confirm_title" = "%@ 已成为你的朋友";
"friend_add_success_confirm_subtitle" = "设置锁屏权限，接收对方的锁屏更新";
"friend_add_success_confirm_button" = "确认";
"friend_add_success_send_title" = "%@ 已成为你的朋友";
"friend_add_success_send_subtitle" = "快来发送照片，霸占好友屏幕吧";
"friend_add_success_send_button" = "立即发送";

// Friend Popup
"friend_popup_add_title" = "添加好友";
"friend_popup_request_title" = "好友申请";
"friend_popup_applied" = "已申请";
"friend_popup_apply" = "申请";
"friend_popup_ignore" = "忽略";
"friend_popup_add" = "添加";

// Edit Profile
"edit_profile_title" = "编辑资料";
"edit_profile_nickname" = "昵称";
"edit_profile_nickname_placeholder" = "请输入新昵称";
"edit_profile_nickname_edit_title" = "修改昵称";

// Image Guide
"image_guide_title" = "图文教程";
"image_guide_step_1" = "点击去设置跳转快捷指令App后点击底部自动化按钮";
"image_guide_step_2" = "点击右上角添加按钮";
"image_guide_step_3" = "选择电子邮件";
"image_guide_step_4" = "选择发件人";
"image_guide_step_5" = "LookLock已为你自动复制邮箱，长按输入框选择粘贴，然后点击完成";
"image_guide_step_6" = "底部框选择立即运行后点击下一步";
"image_guide_step_7" = "选择LookLock，然后就完成啦！";

// Version Update
"version_update_title" = "发现新版本";
"version_update_button" = "更新";
"version_update_later" = "稍后";

// Lock Screen Camera (Consolidated - removed duplicates)
"camera_send_to_all_friends" = "投送给所有好友";
"camera_send_to_friends" = "投送给好友";
"camera_photo_access_denied" = "请在设置中允许访问照片以选择图片";
"camera_sync_preference_question" = "是否同步为我的锁屏？";
"camera_sync_preference_always" = "始终保持该选项，下次不再询问";
"camera_sync_preference_no" = "否";
"camera_sync_preference_yes" = "是";
"camera_upload_failed" = "图片上传失败: %@";
"camera_send_failed" = "投送失败: %@";
"camera_first_success_later" = "下次再说";
"camera_first_success_add_now" = "立即添加";
"camera_first_success_title" = "设置成功!";
"camera_first_success_message" = "添加好友即可霸占对方锁屏!\n快来试试吧~";
"camera_image_load_failed" = "图片加载失败，请重试";
"camera_crop_tip" = "双指缩放、拖动调整";
"camera_flash_on" = "闪光灯已打开";
"camera_flash_off" = "闪光灯已关闭";
"camera_flash_auto" = "闪光灯自动";
"camera_automation_title" = "请完成自动化授权以便自动投送到锁屏";
"camera_automation_go" = "去设置";
"camera_automation_done" = "已设置";
"camera_email_banner_title" = "一键绑定邮箱！";
"camera_email_banner_subtitle" = "打开锁屏就是惊喜";
"camera_email_banner_go" = "GO";

// AC History View
"history_label" = "历史";

// Friend Invite Popup
"friend_invite_success" = "好友申请发送成功";
"friend_invite_failed" = "好友申请发送失败，请稍后重试";

// AC Top Bar View
"topbar_add_friend" = "添加好友";
"topbar_add_friend_now" = "立即添加好友";
"topbar_default_user" = "用户";

// Add Friend View Controller (Consolidated)
"add_friend_invite_title" = "邀请好友加入 LookLock\n一起霸屏，实时掌控对方锁屏画面！";
"add_friend_share_link_title" = "分享 LookLock 链接";
"add_friend_share_wechat" = "微信";
"add_friend_share_qq" = "QQ";
"add_friend_share_copy_code" = "复制口令";
"add_friend_share_more" = "更多";
"add_friend_share_copy" = "Copy";
"add_friend_share_other" = "Other";
"add_friend_share_title_template" = "%@邀请你霸占TA的锁屏";
"add_friend_share_description" = "锁屏暗战启动！👾，偷偷把TA的锁屏变成我的画板🎨";
"add_friend_share_system" = "系统分享";
"add_friend_copy_success" = "复制成功，去粘贴发送给好友";
"add_friend_share_success_template" = "%@分享成功";
"add_friend_share_failed_template" = "%@分享失败：%@";
"add_friend_alert_title" = "提示";
"add_friend_alert_ok" = "确定";

// Shortcut Step Checklist
"shortcut_checklist_install_title" = "安装快捷指令";
"shortcut_checklist_install_subtitle" = "一键获取并运行 LookLock 快捷指令";
"shortcut_checklist_automation_title" = "创建自动化";
"shortcut_checklist_automation_subtitle" = "在快捷指令 App 中创建自动化";
"shortcut_checklist_email_title" = "配置邮箱";
"shortcut_checklist_email_subtitle" = "一键配置专属邮箱";
"shortcut_checklist_cancel" = "取消";
"shortcut_checklist_run" = "去运行";
"shortcut_checklist_run_prompt" = "似乎还没有运行过快捷指令？运行一次，再看看你的锁屏有什么不一样🤔";
"shortcut_checklist_mail_error_title" = "无法发送邮件";
"shortcut_checklist_mail_error_message" = "请前往\"设置 > 邮件\"添加邮件账户后重试。";
"shortcut_checklist_mail_error_ok" = "确定";
"shortcut_checklist_email_subject" = "#%@#用户主动绑定电子邮件";
"shortcut_checklist_email_success" = "发送邮件成功！";

// FAQ
"faq_button_title" = "常见问题？";
"faq_question_1" = "快捷指令运行错误？";
"faq_answer_1" = "LookLock 快捷指令需要给予完整权限，如果执行出错可能是因为频繁执行快捷指令导致，重试一次或者等待 5 分钟";
"faq_question_2" = "如何关闭快捷指令推送？";
"faq_answer_2" = "打开\"设置\"应用。选择\"屏幕使用时间\"。在\"查看所有活动\"中进行详细设置。";
"faq_question_3" = "被朋友投送后锁屏没有变更？";
"faq_answer_3" = "请保证每一步教程都成功执行，看看邮件 APP 里是不是收到了 looklock 的官方邮件，或是被误判进了垃圾邮件里，将 looklock 设置为邮件白名单就行了";

// Removed duplicate Lock Screen Camera keys - consolidated above
"camera_lockscreen_ratio_title" = "锁屏";

// Friend Cell
"friend_cell_send_button" = "投送";
"friend_cell_default_nickname" = "用户id";

// Shortcut Step 0 Demo
"shortcut_step0_title" = "开始霸占TA的锁屏";
"shortcut_step0_description" = "3步搞定，让TA解锁手机第一眼就是你👀";

// Shortcut Step 2 Automation
"shortcut_step2_title" = "创建自动化";
"shortcut_step2_image_guide_button" = "查看图文教程";
"shortcut_step2_settings_button" = "去设置";
"shortcut_step2_copy_success" = "已复制";
"shortcut_step2_open_shortcuts_failed" = "无法打开快捷指令App";

// Share Manager
"share_copy_message_template" = "【复制本条消息】来Looklock，加我为朋友，霸占我的手机锁屏\n%@\nLookLock下载：%@";
"share_error_app_not_installed" = "请先安装%@";
"share_error_sdk_not_configured" = "%@SDK未正确配置";
"share_error_invalid_share_code" = "分享码无效";
"share_error_network_error" = "网络连接失败";
"share_platform_wechat" = "微信";
"share_platform_qq" = "QQ";


