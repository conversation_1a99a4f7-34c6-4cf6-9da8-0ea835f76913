// Common
"ok" = "OK";
"cancel" = "Cancel";
"save" = "Save";
"delete" = "Delete";
"edit" = "Edit";
"back" = "Back";
"continue" = "Continue";
// Legacy keys for backward compatibility
"Terms" = "Terms of Service";
"Privacy Policy" = "Privacy Policy";
"By tapping Continue, you agree to our Terms and Privacy Policy." = "By tapping Continue, you agree to our Terms of Service and Privacy Policy.";

// Error Messages
"error_network" = "Network Error";
"error_server" = "Server Error";
"error_unknown" = "Unknown Error";
"error_invalid_email" = "Please enter a valid email address";
"error_password_length" = "Password must be at least 8 characters";
"error_wrong_password" = "Wrong password";
"error_user_not_found" = "User not found";
"error_user_disabled" = "User has been disabled";
"error_too_many_requests" = "Too many requests, please try again later";
"error_email_already_in_use" = "Email is already in use";
"error_weak_password" = "Password is too weak";
"error_operation_not_allowed" = "Operation not allowed";
"error_unauthorized" = "Session expired, please login again";

// Loading
"loading" = "Loading...";
"loading_failed" = "Loading Failed";

// Alert
"alert_title_error" = "Error";
"alert_title_warning" = "Warning";
"alert_title_success" = "Success";

// Login
"login_title" = "Login";
"login_username" = "Username";
"login_password" = "Password";
"login_button" = "Login";
"login_forgot_password" = "Forgot Password?";
"login_register" = "Register";
"login_email_title" = "What's your email?";
"login_password_title" = "Enter your password";
"login_email_placeholder" = "Enter your email";
"login_password_placeholder" = "Enter your password";
"login_continue" = "Continue";
"login_forgot_password_button" = "Forgot password?";
"login_password_hint" = "Your password must be at least 8 characters";
"login_error_invalid_email" = "Invalid email address.";
"login_error_password_length" = "Password must be at least 8 characters.";
"login_forgot_password_title" = "Hint";
"login_forgot_password_message" = "Please enter a valid email first";
"login_reset_email_sent_title" = "Email Sent";
"login_reset_email_sent_message" = "We have sent a reset link to your email, please check.";
"login_reset_email_sent_title_en" = "Email Sent";
"login_reset_email_sent_message_en" = "Password reset email has been sent. Please check your inbox.";
"login_terms_service" = "Terms of Service";
"login_privacy_policy" = "Privacy Policy";

// Removed duplicate Register keys - consolidated in Register section below

// Avatar
"avatar_uploading" = "Uploading avatar...";
"avatar_upload_success" = "Avatar uploaded successfully";
"avatar_upload_failed" = "Avatar upload failed";
"avatar_select" = "Select Avatar";
"avatar_take_photo" = "Take Photo";
"avatar_choose_library" = "Choose from Library";

// Logout
"logout_button" = "Logout";
"logout_confirm_title" = "Confirm Logout";
"logout_confirm_message" = "Are you sure you want to logout?";

// Avatar Upload
"error_invalid_image" = "Invalid image";
"error_invalid_response" = "Invalid server response";

// Settings
"settings_guide" = "How to Enable Lock Screen Casting";
"settings_sync_to_lock" = "Sync as My Lock Screen";
"settings_set_as_main" = "Also Set as Home Screen";
"settings_keep_orientation" = "Keep Last Used Camera Orientation";
"settings_feedback" = "Feedback";
"settings_privacy" = "Privacy Policy";
"settings_logout" = "Logout";
"settings_lock_with_shake" = "Vibrate When Lock Screen Changes";
"settings_deregister" = "Delete Account";
"settings_deregister_confirm_title" = "Are you sure you want to delete your account?";
"settings_deregister_confirm_message" = "After deletion, your account will be permanently deleted and all data cannot be recovered.";
"settings_deregister_confirm_button" = "Confirm Deletion";
"settings_deregistering" = "Deleting...";
"settings_logout_confirm_title" = "Confirm Logout";
"settings_feedback_error_title" = "Error";
"settings_feedback_error_message" = "Unable to open feedback page";

// Shortcut Guide
"shortcut_guide_greeting" = "Hi, %@";
"shortcut_guide_steps" = "3 Quick Steps";
"shortcut_guide_prev" = "Previous";
"shortcut_guide_next" = "Next";
"shortcut_guide_start" = "Get Started";
"shortcut_guide_complete" = "Complete";

// Intro
"intro_title" = "LookLock";
"intro_slogan" = "Snap to Lock Screen";
"intro_agreement_text" = "By tapping Sign In and using LookLock, you agree to our Terms of Service and Privacy Policy.";
"intro_terms_service" = "Terms of Service";
"intro_privacy_policy" = "Privacy Policy";
"intro_login_failed_title" = "Login Failed";
"intro_login_failed_ok" = "OK";

// Lock Intent
"lock_intent_unauthorized" = "Not logged in or authentication expired, please log in to the app first";
"lock_intent_network_error" = "Network request failed, please check network connection";
"lock_intent_no_image_url" = "No available lock screen image found";
"lock_intent_version_low" = "Shortcut version is too low, please go to APP to upgrade";
"lock_intent_first_init" = "👀For a better experience, please allow all shortcut permissions";

// Image Upload Errors
"image_upload_invalid" = "Invalid or failed to process image";
"image_upload_failed" = "Upload failed: %@";
"image_upload_token_failed" = "Failed to get upload token: %@";
"image_upload_invalid_response" = "Server response exception";
"image_upload_user_not_found" = "User not logged in, please log in again and try again";

// Friends
"friends_title" = "Friends";
"friends_pull_to_refresh" = "Pull to refresh";
"friends_empty_message" = "No friends yet, go add some!";
"friends_empty_button" = "Add New Friend";
"friends_delete_confirm_title" = "Confirm delete this friend?";
"friends_delete_confirm_message" = "After deletion, you will not be able to send photos to their lock screen";
"friends_delete_confirm_button" = "Confirm";

// Removed duplicate Add Friend keys - consolidated in Add Friend View Controller section below

// Popup
"popup_friend_request_success" = "Successfully added friend";
"popup_friend_request_failed" = "Failed to add, please try again";
"popup_operation_failed" = "Operation failed, please try again";

// Removed duplicate Camera keys - consolidated in Lock Screen Camera section below

// Register
"register_email_title" = "What's your email?";
"register_email_placeholder" = "Enter your email";
"register_password_title" = "Set your password";
"register_password_placeholder" = "Set your password";
"register_password_hint" = "Your password must be at least 8 characters";
"register_continue" = "Continue";
"register_button" = "Register";
"register_error_invalid_email" = "Invalid email address.";
"register_error_password_length" = "Password must be at least 8 characters.";
"register_email" = "Continue with Email";

// Photo Preview
"photo_preview_send_to_friend" = "Send to Friend";
"photo_preview_set_as_lockscreen" = "Set as Lock Screen";
"photo_preview_dont_ask_again" = "Don't ask again";
"photo_preview_ok" = "OK";
"photo_preview_lockscreen_tip" = "If setting lock screen fails because the previous lock screen was occupied for too short a time, just set it again";

// History
"history_empty_message" = "No history records";
"history_load_more_failed" = "Failed to load more, please pull to refresh and try again";

// Friend Selection
"friend_selection_title" = "Who can see";
"friend_selection_confirm" = "Confirm";
"friend_selection_all_friends" = "All friends can see";
"friend_selection_specific_friends" = "Specific friends can see";

// Friend Add Success
"friend_add_success_later" = "Maybe Later";
"friend_add_success_confirm_title" = "%@ is now your friend";
"friend_add_success_confirm_subtitle" = "Set lock screen permissions to receive their lock screen updates";
"friend_add_success_confirm_button" = "Confirm";
"friend_add_success_send_title" = "%@ is now your friend";
"friend_add_success_send_subtitle" = "Send photos and take over your friend's screen!";
"friend_add_success_send_button" = "Send Now";

// Friend Popup
"friend_popup_add_title" = "Add Friend";
"friend_popup_request_title" = "Friend Request";
"friend_popup_applied" = "Applied";
"friend_popup_apply" = "Apply";
"friend_popup_ignore" = "Ignore";
"friend_popup_add" = "Add";

// Edit Profile
"edit_profile_title" = "Edit Profile";
"edit_profile_nickname" = "Nickname";
"edit_profile_nickname_placeholder" = "Enter new nickname";
"edit_profile_nickname_edit_title" = "Edit Nickname";

// Image Guide
"image_guide_title" = "Tutorial";
"image_guide_step_1" = "Click Go to Settings to jump to Shortcuts App, then click the Automation button at the bottom";
"image_guide_step_2" = "Click the Add button in the top right corner";
"image_guide_step_3" = "Select Email";
"image_guide_step_4" = "Select Sender";
"image_guide_step_5" = "LookLock has automatically copied the email for you, long press the input box to select paste, then click Done";
"image_guide_step_6" = "Select Run Immediately at the bottom, then click Next";
"image_guide_step_7" = "Select LookLock, and you're done!";

// Version Update
"version_update_title" = "New Version Available";
"version_update_button" = "Update";
"version_update_later" = "Later";

// Lock Screen Camera (Consolidated - unified key names)
"camera_send_to_all_friends" = "Send to All Friends";
"camera_send_to_friends" = "Send to Friends";
"camera_photo_access_denied" = "Please allow photo access in Settings to select images";
"camera_sync_preference_question" = "Sync as my lock screen?";
"camera_sync_preference_always" = "Always keep this option, don't ask again";
"camera_sync_preference_no" = "No";
"camera_sync_preference_yes" = "Yes";
"camera_upload_failed" = "Image upload failed: %@";
"camera_send_failed" = "Send failed: %@";
"camera_first_success_later" = "Maybe Later";
"camera_first_success_add_now" = "Add Now";
"camera_first_success_title" = "Setup Successful!";
"camera_first_success_message" = "Add friends to hijack their lock screen!\nLet's do this! 🔥";
"camera_image_load_failed" = "Image loading failed, please try again";
"camera_crop_tip" = "Pinch to zoom, drag to adjust";
"camera_flash_on" = "Flash On";
"camera_flash_off" = "Flash Off";
"camera_flash_auto" = "Flash Auto";
"camera_automation_title" = "Please complete automation authorization for automatic lock screen sync";
"camera_automation_go" = "Go to Settings";
"camera_automation_done" = "Already Set";
"camera_email_banner_title" = "One-click email binding!";
"camera_email_banner_subtitle" = "Surprise when you open the lock screen";
"camera_email_banner_go" = "GO";

// AC History View
"history_label" = "History";

// Friend Invite Popup
"friend_invite_success" = "Friend request sent successfully";
"friend_invite_failed" = "Failed to send friend request, please try again later";

// AC Top Bar View
"topbar_add_friend" = "Add Friend";
"topbar_add_friend_now" = "Add Friend Now";
"topbar_default_user" = "User";

// Add Friend View Controller
"add_friend_invite_title" = "Invite friends to join LookLock\nHijack screens together and control their lock screen in real time!";
"add_friend_share_link_title" = "Share LookLock Link";
"add_friend_share_wechat" = "WeChat";
"add_friend_share_qq" = "QQ";
"add_friend_share_copy_code" = "Copy Code";
"add_friend_share_more" = "More";
"add_friend_share_copy" = "Copy";
"add_friend_share_other" = "Other";
"add_friend_share_title_template" = "%@ invites you to take over their lock screen";
"add_friend_share_description" = "Lock screen battle begins! 👾 Secretly turn their lock screen into my canvas 🎨";
"add_friend_share_system" = "System Share";
"add_friend_copy_success" = "Copied successfully, go paste and send to friends";
"add_friend_share_success_template" = "%@ shared successfully";
"add_friend_share_failed_template" = "%@ share failed: %@";
"add_friend_alert_title" = "Notice";
"add_friend_alert_ok" = "OK";

// Shortcut Step Checklist
"shortcut_checklist_install_title" = "Install Shortcut";
"shortcut_checklist_install_subtitle" = "Get and run LookLock shortcut with one tap";
"shortcut_checklist_automation_title" = "Create Automation";
"shortcut_checklist_automation_subtitle" = "Create automation in Shortcuts App";
"shortcut_checklist_email_title" = "Configure Email";
"shortcut_checklist_email_subtitle" = "One-tap email configuration";
"shortcut_checklist_cancel" = "Cancel";
"shortcut_checklist_run" = "Go Run";
"shortcut_checklist_run_prompt" = "Seems like you haven't run the shortcut yet? Run it once and see what's different on your lock screen 🤔";
"shortcut_checklist_mail_error_title" = "Cannot Send Email";
"shortcut_checklist_mail_error_message" = "Please go to \"Settings > Mail\" to add a mail account and try again.";
"shortcut_checklist_mail_error_ok" = "OK";
"shortcut_checklist_email_subject" = "#%@#User actively binds email address";
"shortcut_checklist_email_success" = "Email sent successfully!";

// FAQ
"faq_button_title" = "FAQ";
"faq_question_1" = "Shortcut execution error?";
"faq_answer_1" = "LookLock Shortcut requires full permissions. If execution fails, it might be due to running the shortcut too frequently. Try again or wait 5 minutes";
"faq_question_2" = "How to turn off shortcut notifications?";
"faq_answer_2" = "Open the \"Settings\" app. Select \"Screen Time\". Configure detailed settings in \"See All Activity\".";
"faq_question_3" = "Lock screen didn't change after friend sent?";
"faq_answer_3" = "Please ensure every tutorial step was completed successfully. Check if you received the official LookLock email in your Mail app, or if it was filtered into spam. Add LookLock to your email whitelist";

// Removed duplicate Lock Screen Camera keys - consolidated above
"camera_lockscreen_ratio_title" = "Lock Screen";

// Friend Cell
"friend_cell_send_button" = "Send";
"friend_cell_default_nickname" = "User ID";

// Shortcut Step 0 Demo
"shortcut_step0_title" = "Hijack Their Lock Screen";
"shortcut_step0_description" = "3 easy steps to become their first sight every unlock 👀";

// Shortcut Step 2 Automation
"shortcut_step2_title" = "Create Automation";
"shortcut_step2_image_guide_button" = "View Tutorial";
"shortcut_step2_settings_button" = "Go to Settings";
"shortcut_step2_copy_success" = "Copied";
"shortcut_step2_open_shortcuts_failed" = "Unable to open Shortcuts App";

// Share Manager
"share_copy_message_template" = "【Copy this message】Join me on LookLock and hijack my phone's lock screen! 📱✨\n%@\nDownload LookLock: %@";
"share_error_app_not_installed" = "Please install %@ first";
"share_error_sdk_not_configured" = "%@ SDK not properly configured";
"share_error_invalid_share_code" = "Invalid share code";
"share_error_network_error" = "Network connection failed";
"share_platform_wechat" = "WeChat";
"share_platform_qq" = "QQ";

// Removed duplicate keys - already defined above
"connecting" = "Connecting";
"continue_with_apple" = "Continue with Apple";
"continue_with_google" = "Continue with Google";
"continue_with_qq" = "Continue with QQ";
"continue_with_wechat" = "Continue with WeChat";
"login_failed" = "Login failed";
"login_success" = "Login successful";
"third_party_auth_configuration_error" = "Configuration error";
"third_party_auth_invalid_credentials" = "Invalid credentials";
"third_party_auth_network_error" = "Network error";
"third_party_auth_service_unavailable" = "Service unavailable";
"third_party_auth_unknown_error" = "Unknown error";
"third_party_auth_user_cancelled" = "User cancelled";


