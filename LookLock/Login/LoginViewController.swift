import UIKit
import FirebaseAuth
import SnapKit

class LoginViewController: BaseViewController {
    
    private let titleLabel = UILabel()
    private let emailTextField = UITextField()
    private let passwordTextField = UITextField()
    private let loginButton = UIButton()
    private let containerView = UIView()
    private let stackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 30
        stack.alignment = .fill
        stack.distribution = .fill
        return stack
    }()
    private enum Step { case email, password }
    private var currentStep: Step = .email
    private var currentEmail: String?
    private let agreementLabel = UILabel()
    private var bgImage = UIImageView(image: UIImage(named: "login_top"))
    private let forgotPasswordButton = UIButton(type: .system)
    private let passwordHintLabel = UILabel()

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UII<PERSON>(named: "main_back"),
            style: .plain,
            target: self,
            action: #selector(backButtonTapped)
        )
        navigationItem.leftBarButtonItem?.tintColor = .white
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
    }

    override func setupLocalization() {
        // 更新标题
        titleLabel.text = currentStep == .password ? "login_password_title".localized : "login_email_title".localized

        // 更新占位符
        emailTextField.placeholder = "login_email_placeholder".localized
        emailTextField.attributedPlaceholder = NSAttributedString(string: "login_email_placeholder".localized, attributes: [NSAttributedString.Key.foregroundColor: UIColor.init(white: 1, alpha: 0.6)])
        passwordTextField.placeholder = "login_password_placeholder".localized

        // 更新按钮文本
        loginButton.setTitle(currentStep == .password ? "login_button".localized : "login_continue".localized, for: .normal)
        forgotPasswordButton.setTitle("login_forgot_password_button".localized, for: .normal)

        // 更新提示文本
        passwordHintLabel.text = "login_password_hint".localized

        // 更新协议文本
        let agreementText = NSMutableAttributedString(string: "By tapping Continue, you agree to our Terms and Privacy Policy.".localized)
        let termsRange = (agreementText.string as NSString).range(of: "Terms".localized)
        let privacyRange = (agreementText.string as NSString).range(of: "Privacy Policy".localized)

        agreementText.addAttribute(.foregroundColor, value: UIColor(hex: "#676767"), range: NSRange(location: 0, length: agreementText.length))

        agreementText.addAttributes([
            .foregroundColor: UIColor.white
        ], range: termsRange)

        agreementText.addAttributes([
            .foregroundColor: UIColor.white
        ], range: privacyRange)

        agreementLabel.attributedText = agreementText
    }
    
    @objc private func backButtonTapped() {
        if currentStep == .password {
            animateStep(toPassword: false)
        } else {
            navigationController?.popViewController(animated: true)
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.emailTextField.becomeFirstResponder()
    }
    override func setupUI() {
        view.backgroundColor = .black
        bgImage.contentMode = .scaleAspectFill
        bgImage.frame = view.bounds
        view.addSubview(bgImage)
        view.addSubview(containerView)
        containerView.addSubview(stackView)
        // 标题
        titleLabel.text = "login_email_title".localized
        titleLabel.font = .systemFont(ofSize: 26, weight: .semibold)
        titleLabel.textColor = .white
        titleLabel.textAlignment = .center
        stackView.addArrangedSubview(titleLabel)
        // 邮箱输入
        emailTextField.placeholder = "login_email_placeholder".localized
        emailTextField.attributedPlaceholder = NSAttributedString(string: "login_email_placeholder".localized, attributes: [NSAttributedString.Key.foregroundColor: UIColor.init(white: 1, alpha: 0.6)])
        emailTextField.keyboardType = .emailAddress
        emailTextField.autocapitalizationType = .none
        emailTextField.autocorrectionType = .no
        emailTextField.textColor = .white
        emailTextField.borderStyle = .none
        emailTextField.delegate = self
        emailTextField.addTarget(self, action: #selector(emailTextFieldChanged), for: .editingChanged)
        emailTextField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        emailTextField.leftViewMode = .always
        emailTextField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        emailTextField.rightViewMode = .always
        emailTextField.backgroundColor = UIColor(hex: "#141414")
        emailTextField.tintColor = ACStyle.brandColor
        emailTextField.layer.cornerRadius = 16
        emailTextField.layer.masksToBounds = true
        stackView.addArrangedSubview(emailTextField)
        // 密码输入
        passwordTextField.placeholder = "login_password_placeholder".localized
        passwordTextField.isSecureTextEntry = true
        passwordTextField.textColor = .white
        passwordTextField.backgroundColor = UIColor(hex: "#141414")
        passwordTextField.layer.cornerRadius = 16
        passwordTextField.layer.masksToBounds = true
        passwordTextField.borderStyle = .none
        passwordTextField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        passwordTextField.leftViewMode = .always
        passwordTextField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        passwordTextField.rightViewMode = .always
        passwordTextField.tintColor = ACStyle.brandColor
        passwordTextField.delegate = self
        passwordTextField.addTarget(self, action: #selector(passwordTextFieldChanged), for: .editingChanged)
        passwordTextField.isHidden = true
        stackView.addArrangedSubview(passwordTextField)
        // 登录按钮
        loginButton.setTitle("login_continue".localized, for: .normal)
        loginButton.backgroundColor = UIColor(hex: "#242424", alpha: 1)
        loginButton.layer.cornerRadius = 25
        loginButton.isEnabled = false
        loginButton.setTitleColor(UIColor.init(white: 1, alpha: 0.4), for: .normal)
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        containerView.addSubview(loginButton)
        // 协议
        let agreementText = NSMutableAttributedString(string: "By tapping Continue, you agree to our Terms and Privacy Policy.".localized)
        
        let termsRange = (agreementText.string as NSString).range(of: "Terms".localized)
        let privacyRange = (agreementText.string as NSString).range(of: "Privacy Policy".localized)
        
        agreementText.addAttribute(.foregroundColor, value: UIColor(hex: "#676767"), range: NSRange(location: 0, length: agreementText.length))
        
        agreementText.addAttributes([
            .foregroundColor: UIColor.white
        ], range: termsRange)
        
        agreementText.addAttributes([
            .foregroundColor: UIColor.white
        ], range: privacyRange)
        
        agreementLabel.attributedText = agreementText
        agreementLabel.textAlignment = .center
        agreementLabel.font = .systemFont(ofSize: 14, weight: .regular)
        agreementLabel.numberOfLines = 0
        agreementLabel.isUserInteractionEnabled = true
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(agreementTapped))
        agreementLabel.addGestureRecognizer(tapGesture)
        
        containerView.addSubview(agreementLabel)
        // Forgot Password Button
        forgotPasswordButton.setTitle("login_forgot_password_button".localized, for: .normal)
        forgotPasswordButton.setTitleColor(ACStyle.brandColor, for: .normal)
        forgotPasswordButton.titleLabel?.font = .systemFont(ofSize: 14)
        forgotPasswordButton.isHidden = true
        forgotPasswordButton.addTarget(self, action: #selector(forgotPasswordTapped), for: .touchUpInside)
        containerView.addSubview(forgotPasswordButton)
        // Password Hint Label
        passwordHintLabel.text = "login_password_hint".localized
        passwordHintLabel.textColor = UIColor(hex: "#676767")
        passwordHintLabel.font = .systemFont(ofSize: 14)
        passwordHintLabel.isHidden = true
        containerView.addSubview(passwordHintLabel)
    }
    
    override func setupConstraints() {
        bgImage.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        stackView.snp.makeConstraints { make in
            make.centerX.equalTo(containerView)
            make.centerY.equalTo(containerView).offset(-30)
            make.leading.trailing.equalTo(containerView).inset(16)
        }
        emailTextField.snp.makeConstraints { make in
            make.height.equalTo(54)
        }
        passwordTextField.snp.makeConstraints { make in
            make.height.equalTo(54)
        }
        loginButton.snp.makeConstraints { make in
            make.bottom.equalTo(agreementLabel.snp.top).offset(-20)
            make.leading.trailing.equalTo(containerView).inset(20)
            make.height.equalTo(50)
        }
        
        agreementLabel.snp.makeConstraints { make in
            make.bottom.equalTo(containerView).offset(-10)
            make.leading.trailing.equalTo(containerView).inset(46)
        }
        // Forgot / Hint
        forgotPasswordButton.snp.makeConstraints { make in
            make.top.equalTo(passwordTextField.snp.bottom).offset(8)
            make.trailing.equalTo(passwordTextField)
        }
        passwordHintLabel.snp.makeConstraints { make in
            make.top.equalTo(passwordTextField.snp.bottom).offset(8)
            make.leading.equalTo(passwordTextField)
        }
    }
    
    @objc private func emailTextFieldChanged() {
        if currentStep == .email {
            let isValid = isValidEmail(emailTextField.text ?? "")
            loginButton.isEnabled = isValid
            loginButton.backgroundColor = isValid ? ACStyle.brandColor : UIColor(hex: "#242424", alpha: 1)
            loginButton.setTitleColor(isValid ? UIColor(hex: "#121212", alpha: 1) : UIColor.init(white: 1, alpha: 0.4), for: .normal)
        }
    }
    @objc private func passwordTextFieldChanged() {
        if currentStep == .password {
            let isValid = (passwordTextField.text ?? "").count >= 8
            loginButton.isEnabled = isValid
            loginButton.backgroundColor = isValid ? ACStyle.brandColor : UIColor(hex: "#242424", alpha: 1)
            loginButton.setTitleColor(isValid ? UIColor(hex: "#121212", alpha: 1) : UIColor.init(white: 1, alpha: 0.4), for: .normal)
            updatePasswordSubViews()
        }
    }
    private func updatePasswordSubViews() {
        let hasInput = !(passwordTextField.text ?? "").isEmpty
        passwordHintLabel.isHidden = !hasInput
        forgotPasswordButton.isHidden = hasInput
    }
    @objc private func loginButtonTapped() {
        switch currentStep {
        case .email:
            guard let email = emailTextField.text, isValidEmail(email) else {
                showAlert(title: "alert_title_error".localized, message: "login_error_invalid_email".localized)
                return
            }
            currentEmail = email
            animateStep(toPassword: true)
        case .password:
            guard let password = passwordTextField.text, password.count >= 8 else {
                showAlert(title: "alert_title_error".localized, message: "login_error_password_length".localized)
                return
            }
            guard let email = currentEmail else { return }
            showLoading()
            let authProvider = AuthProviderFactory.provider()
            authProvider.register(email: email, password: password) { [weak self] result in
                self?.hideLoading()
                switch result {
                case .success:
                    // 对于 Firebase 流程，可能返回 user
                    
                    // 登录成功后，同步好友列表以及可能的好友请求，避免进入主界面后数据为空
                    FriendRepository.shared.refresh(trigger: .launch)
                    // 跳转到主界面
                    self?.navigateToMainApp()
                case .failure(let error):
                    self?.handleRegisterError(error)
                }
            }
        }
    }
    private func animateStep(toPassword: Bool) {
        // 1. 创建当前视图的快照
        guard let snapshot = stackView.snapshotView(afterScreenUpdates: false) else { return }
        snapshot.frame = stackView.frame
        containerView.addSubview(snapshot)
        
        // 2. 更新 UI 状态
        currentStep = toPassword ? .password : .email
        titleLabel.text = toPassword ? "login_password_title".localized : "login_email_title".localized
        emailTextField.isHidden = toPassword
        passwordTextField.isHidden = !toPassword
        loginButton.setTitle(toPassword ? "login_button".localized : "login_continue".localized, for: .normal)
        agreementLabel.isHidden = toPassword
        
        if toPassword {
            loginButton.isEnabled = false
            passwordTextField.text = ""
            passwordTextFieldChanged()
            // 显示忘记密码/提示
            updatePasswordSubViews()
        } else {
            let isValid = isValidEmail(emailTextField.text ?? "")
            loginButton.isEnabled = isValid
            loginButton.backgroundColor = isValid ? ACStyle.brandColor : UIColor(hex: "#242424", alpha: 1)
            loginButton.setTitleColor(isValid ? UIColor(hex: "#121212", alpha: 1) : UIColor.init(white: 1, alpha: 0.4), for: .normal)
            // 隐藏忘记密码和提示
            forgotPasswordButton.isHidden = true
            passwordHintLabel.isHidden = true
        }
        
        // 3. 设置初始状态
        stackView.alpha = 0
        let translateX = toPassword ? stackView.frame.width : -stackView.frame.width
        stackView.transform = CGAffineTransform(translationX: translateX, y: 0)
        
        // 4. 切换输入框焦点
        DispatchQueue.main.async {
            if toPassword {
                self.passwordTextField.becomeFirstResponder()
            } else {
                self.emailTextField.becomeFirstResponder()
            }
        }
        
        // 5. 创建弹性动画参数
        let springTiming = UISpringTimingParameters(
            mass: 1.0,          // 质量
            stiffness: 100.0,   // 刚度
            damping: 12.0,      // 阻尼
            initialVelocity: CGVector(dx: toPassword ? 1 : -1, dy: 0)  // 初始速度
        )
        
        // 6. 创建动画器
        let animator = UIViewPropertyAnimator(duration: 0.4, timingParameters: springTiming)
        
        // 7. 添加主动画
        animator.addAnimations {
            // 快照向相反方向移动
            snapshot.transform = CGAffineTransform(translationX: -translateX, y: 0)
            snapshot.alpha = 0
            
            // 新视图淡入并移动到原位
            self.stackView.alpha = 1
            self.stackView.transform = .identity
            
            // 标题动画
            self.titleLabel.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }
        
        // 8. 添加标题恢复动画
        animator.addAnimations({
            self.titleLabel.transform = .identity
        }, delayFactor: 0.5)
        
        // 9. 添加完成回调
        animator.addCompletion { _ in
            snapshot.removeFromSuperview()
            self.view.layoutIfNeeded()
        }
        
        // 10. 启动动画
        animator.startAnimation()
    }
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    private func handleLoginError(_ error: Error) {
        let errorMessage: String
        switch AuthErrorCode(_bridgedNSError: error as NSError)?.code {
        case .invalidEmail:
            errorMessage = "error_invalid_email".localized
        case .wrongPassword:
            errorMessage = "error_wrong_password".localized
        case .userNotFound:
            errorMessage = "error_user_not_found".localized
        case .userDisabled:
            errorMessage = "error_user_disabled".localized
        case .tooManyRequests:
            errorMessage = "error_too_many_requests".localized
        default:
            errorMessage = "error_unknown".localized
        }
        showAlert(title: "alert_title_error".localized, message: errorMessage)
    }
    @objc private func agreementTapped() {
        // 可选：弹出协议页面
        let alertController = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        let termsAction = UIAlertAction(title: "login_terms_service".localized, style: .default) { _ in
            if let url = URL(string: "https://www.actiontech.online/terms-and-conditions/") {
                Router.shared.navigate(to: .agreement(url: url), from: self)
            }
        }
        let privacyAction = UIAlertAction(title: "login_privacy_policy".localized, style: .default) { _ in
            if let url = URL(string: "https://www.freeprivacypolicy.com/live/************************************ ") {
                Router.shared.navigate(to: .agreement(url: url), from: self)
            }
        }
        let cancelAction = UIAlertAction(title: "cancel".localized, style: .cancel)
        alertController.addAction(termsAction)
        alertController.addAction(privacyAction)
        alertController.addAction(cancelAction)
        present(alertController, animated: true)
    }
    @objc private func keyboardWillShow(notification: NSNotification) {
        if let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            UIView.animate(withDuration: 0.3) {
                self.containerView.snp.remakeConstraints { make in
                    make.top.left.right.equalTo(self.view.safeAreaLayoutGuide)
                    make.bottom.equalTo(self.view.safeAreaLayoutGuide).offset(-keyboardSize.height)
                }
                self.view.layoutIfNeeded()
            }
        }
    }
    @objc private func keyboardWillHide(notification: NSNotification) {
        UIView.animate(withDuration: 0.3) {
            self.containerView.snp.remakeConstraints { make in
                make.edges.equalTo(self.view.safeAreaLayoutGuide)
            }
            self.view.layoutIfNeeded()
        }
    }
    @objc private func forgotPasswordTapped() {
        guard let email = currentEmail, isValidEmail(email) else {
            showAlert(title: "login_forgot_password_title".localized, message: "login_forgot_password_message".localized)
            return
        }
 
        if LoginProvider.last == .native || (LoginProvider.last == nil && RegionResolver.current == .mainland) {
            NetworkManager.shared.resetPassword(email: email) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        self?.showAlert(title: "login_reset_email_sent_title".localized, message: "login_reset_email_sent_message".localized)
                    case .failure(let error):
                        self?.showAlert(title: "alert_title_error".localized, message: error.localizedDescription)
                    }
                }
            }
        } else {

            Auth.auth().sendPasswordReset(withEmail: email) { [weak self] error in
                DispatchQueue.main.async {
                    if let error = error {
                        self?.showAlert(title: "alert_title_error".localized, message: error.localizedDescription)
                    } else {
                        self?.showAlert(title: "login_reset_email_sent_title_en".localized, message: "login_reset_email_sent_message_en".localized)
                    }
                }
            }
        }
    }
    
    private func handleRegisterError(_ error: Error) {
        var errorMessage: String = ""
        if let authErrorCode = AuthErrorCode(rawValue: (error as NSError).code) {
            
            switch authErrorCode {
            case .emailAlreadyInUse:
                guard let password = passwordTextField.text, password.count >= 8 else {
                    showAlert(title: "alert_title_error".localized, message: "login_error_password_length".localized)
                    return
                }
                guard let email = currentEmail else { return }
                showLoading()
                let authProvider = AuthProviderFactory.provider()
                authProvider.login(email: email, password: password) { [weak self] result in
                    switch result {
                    case .success(let loginResponse):
                        if let userInfo = loginResponse.userInfo {
                            let profile = UserProfile(
                                userId: userInfo.userId,
                                email: userInfo.email,
                                nickname: userInfo.nickname,
                                avatar: userInfo.avatar,
                                birthday: userInfo.birthday,
                                sex: userInfo.sex,
                                shareCode: userInfo.shareCode
                            )
                            UserDefaults.standard.set(loginResponse.easfilePath, forKey: AppUserDefaultsKeys.easFileUrl)
                            Task {
                                try? await UserService.shared.saveUserProfile(profile)
                            }
                        }
                        // 登录成功后，同步好友列表以及可能的好友请求，避免进入主界面后数据为空
                        FriendRepository.shared.refresh(trigger: .launch)
                        // 跳转到主界面
                        self?.navigateToMainApp()
                    case .failure(let error):
                        self?.handleLoginError(error)
                    }
                }
                return
            case .weakPassword:
                errorMessage = "error_weak_password".localized
            case .invalidEmail:
                errorMessage = "error_invalid_email".localized
            case .networkError:
                errorMessage = "error_network".localized
            default:
                errorMessage = "error_unknown".localized
            }
            showAlert(title: "alert_title_error".localized, message: errorMessage)
        }else {
            switch (error as? NetworkError) {
            case .custom(let msg):
                errorMessage = msg
            default:
                break
            }
        }
        showAlert(title: "alert_title_error".localized, message: errorMessage)
    }

    private func navigateToMainApp() {
        // 跳转到主应用界面
        let mainVC = ACMainContainerViewController()
        let navController = UINavigationController(rootViewController: mainVC)

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            UIView.transition(with: window, duration: 0.3, options: .transitionCrossDissolve, animations: {
                window.rootViewController = navController
            })
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

extension LoginViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if currentStep == .email {
            if isValidEmail(emailTextField.text ?? "") {
                loginButtonTapped()
                return true
            }
        } else if currentStep == .password {
            if (passwordTextField.text ?? "").count >= 8 {
                loginButtonTapped()
                return true
            }
        }
        return false
    }
} 
