<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "A0F82AFC-20BF-4D72-8082-F80D34D041B2"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1955CFC0-1A80-4613-861A-A3328B82EDBA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Register/RegisterProfileViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "124"
            endingLineNumber = "124"
            landmarkName = "completeButtonTapped()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "70E8DD6F-C612-45D1-8374-A9E316C65AE1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/AvatarUploaderService.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "59"
            endingLineNumber = "59">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "310060FC-359B-41F7-9945-7D2B374466FD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Models/UserProfile.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "56"
            endingLineNumber = "56">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BACCDB44-8D44-4010-890D-01645C8E3F35"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Utilities/ACUniversalLinkHandler.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "174"
            endingLineNumber = "174">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8D724F84-0071-4669-8802-9DF150FC6E3A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/SocialRequestCoordinator.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "87"
            endingLineNumber = "87">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C00AF82F-FD0C-4171-B36D-77160B6E3D40"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/AboutCamera/LockScreenCameraViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "806"
            endingLineNumber = "806"
            landmarkName = "LockScreenCameraViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E5240EBE-8A6D-49F6-B61E-BD066038E635"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Popup/FriendRequestPopup.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "45"
            endingLineNumber = "45"
            landmarkName = "present(in:completion:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "E5240EBE-8A6D-49F6-B61E-BD066038E635 - 93425ab2192a4c0d"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (LookLock.FriendPopupViewController.FriendPopupAction) -&gt; () in LookLock.FriendRequestPopup.present(in: __C.UIViewController, completion: () -&gt; ()) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Popup/FriendRequestPopup.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "45"
                  endingLineNumber = "45">
               </Location>
               <Location
                  uuid = "E5240EBE-8A6D-49F6-B61E-BD066038E635 - 987e695f3ab91125"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #2 () async -&gt; () in closure #1 (LookLock.FriendPopupViewController.FriendPopupAction) -&gt; () in LookLock.FriendRequestPopup.present(in: __C.UIViewController, completion: () -&gt; ()) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Popup/FriendRequestPopup.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "45"
                  endingLineNumber = "45">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "21B26830-8236-45D2-A985-49C56AD5EDBE"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Popup/FriendRequestPopup.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "49"
            endingLineNumber = "49"
            landmarkName = "present(in:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C5E3C224-DC97-4F3E-A28C-74BD980C0C0B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Popup/FriendRequestPopup.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "47"
            endingLineNumber = "47"
            landmarkName = "present(in:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0604B1B3-C415-4AA7-9057-6BDF83F80DAD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Friends/FriendPopupViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "184"
            endingLineNumber = "184"
            landmarkName = "applyTapped()"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "0604B1B3-C415-4AA7-9057-6BDF83F80DAD - 385f7e0029f321ba"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "LookLock.FriendPopupViewController.applyTapped() -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Friends/FriendPopupViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "184"
                  endingLineNumber = "184">
               </Location>
               <Location
                  uuid = "0604B1B3-C415-4AA7-9057-6BDF83F80DAD - b0486a62727e7eae"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 () -&gt; () in LookLock.FriendPopupViewController.applyTapped() -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Friends/FriendPopupViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "184"
                  endingLineNumber = "184">
               </Location>
               <Location
                  uuid = "0604B1B3-C415-4AA7-9057-6BDF83F80DAD - 1b8bdb137fda99e2"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 () -&gt; () in closure #1 () -&gt; () in LookLock.FriendPopupViewController.applyTapped() -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Friends/FriendPopupViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "184"
                  endingLineNumber = "184">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "985D72A5-2654-41AB-9AD2-57AE4FBB5922"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Friends/FriendSelectionViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "160"
            endingLineNumber = "160"
            landmarkName = "updateConfirmButtonState()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7C965EF1-08CE-4E5B-A65B-3DFADDB062ED"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/AboutCamera/LockScreenCameraViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "440"
            endingLineNumber = "440"
            landmarkName = "handlePinch(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C9691CD9-AF75-4F15-A5F7-2CBA4CE64A31"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/AboutCamera/LockScreenCameraViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "806"
            endingLineNumber = "806"
            landmarkName = "LockScreenCameraViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7FDFA5A2-624E-47A4-9482-42507EF42238"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Setting/ShortcutStep2AutomationVC.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "128"
            endingLineNumber = "128"
            landmarkName = "setupVideoPlayer()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "43F712AD-1668-42AC-BEC2-E530C37854A2"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Setting/ShortcutGuideViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "317"
            endingLineNumber = "317"
            landmarkName = "updatePageControlAndButtons()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E2925994-281B-42A4-A865-8511BA04D48A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Setting/ShortcutGuideViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "317"
            endingLineNumber = "317"
            landmarkName = "updatePageControlAndButtons()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A8886317-EE49-4A6A-8B48-8D715FAD31AA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/QiniuImageUploader.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "75"
            endingLineNumber = "75"
            landmarkName = "upload(image:key:maxSize:quality:permanent:progress:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "75B63C91-0ED4-43BE-935C-81AF105A5B8C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/QiniuImageUploader.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "162"
            endingLineNumber = "162"
            landmarkName = "compressImage(_:maxSideInPixels:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "75B63C91-0ED4-43BE-935C-81AF105A5B8C - dc7e310b3734e65b"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "LookLock.QiniuImageUploader.compressImage(_: __C.UIImage, maxSideInPixels: CoreGraphics.CGFloat) -&gt; Swift.Optional&lt;__C.UIImage&gt;"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Services/QiniuImageUploader.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "162"
                  endingLineNumber = "162">
               </Location>
               <Location
                  uuid = "75B63C91-0ED4-43BE-935C-81AF105A5B8C - ce4d37ba18dd194"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (__C.UIGraphicsImageRendererContext) -&gt; () in LookLock.QiniuImageUploader.compressImage(_: __C.UIImage, maxSideInPixels: CoreGraphics.CGFloat) -&gt; Swift.Optional&lt;__C.UIImage&gt;"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Services/QiniuImageUploader.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "163"
                  endingLineNumber = "163">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "532F2584-A1F2-43A2-AD23-72F60E1FCA66"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/QiniuImageUploader.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "81"
            endingLineNumber = "81"
            landmarkName = "upload(image:key:maxSize:quality:permanent:progress:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "154C12D3-12B5-47A4-BD83-3BD4455B0CB7"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Setting/EditProfileViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "52"
            endingLineNumber = "52"
            landmarkName = "updateAvatar(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "965F3912-154D-43E3-9B19-BB7AF4EC871B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Auth/ThirdParty/QQLoginManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "98"
            endingLineNumber = "98"
            landmarkName = "isQQInstalled()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "03BA8C46-8B7C-4010-946E-00B779CF8C3E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Auth/ThirdParty/QQLoginManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "115"
            endingLineNumber = "115"
            landmarkName = "processLoginSuccess(accessToken:openID:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "03E7A95C-AE1A-41FE-88E1-77905F20F9D2"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Auth/ThirdParty/QQLoginManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "195"
            endingLineNumber = "195"
            landmarkName = "tencentDidNotLogin(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B15CE70E-0098-4F77-927B-743C3DEAE65B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Auth/ThirdParty/QQLoginManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "207"
            endingLineNumber = "207"
            landmarkName = "QQLoginManager"
            landmarkType = "21">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0EB9DB03-BD7D-4E92-A8E2-7E43640105AA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Auth/Providers/FirebaseAuthProvider.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "87"
            endingLineNumber = "87"
            landmarkName = "performGoogleLogin(presentingViewController:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "29689C61-FF4D-4D80-9BEA-3C037756B46E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Auth/Providers/FirebaseAuthProvider.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "73"
            endingLineNumber = "73"
            landmarkName = "performGoogleLogin(presentingViewController:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AFA92A12-0A30-4041-A78F-031E832852CD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/PushTokenManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "211"
            endingLineNumber = "211"
            landmarkName = "updatePushTokenIfNeeded(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DB4F6407-98A7-43BC-A3F8-E19F2400F4B6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Login/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "464"
            endingLineNumber = "464"
            landmarkName = "handleRegisterError(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "44640B6F-D10A-44BD-A403-00F1F6C9709D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Login/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "374"
            endingLineNumber = "374"
            landmarkName = "handleLoginError(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "24F34D1B-1E0E-4C92-B05D-740924CCCAB6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Login/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "473"
            endingLineNumber = "473"
            landmarkName = "handleRegisterError(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "24F34D1B-1E0E-4C92-B05D-740924CCCAB6 - 86847fea14a22f6"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Result&lt;LookLock.LoginResponse, Swift.Error&gt;) -&gt; () in LookLock.LoginViewController.handleRegisterError(Swift.Error) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Login/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "474"
                  endingLineNumber = "474">
               </Location>
               <Location
                  uuid = "24F34D1B-1E0E-4C92-B05D-740924CCCAB6 - 86847fea14a22f6"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Result&lt;LookLock.LoginResponse, Swift.Error&gt;) -&gt; () in LookLock.LoginViewController.handleRegisterError(Swift.Error) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Login/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "474"
                  endingLineNumber = "474">
               </Location>
               <Location
                  uuid = "24F34D1B-1E0E-4C92-B05D-740924CCCAB6 - 86847fea14a22f6"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Result&lt;LookLock.LoginResponse, Swift.Error&gt;) -&gt; () in LookLock.LoginViewController.handleRegisterError(Swift.Error) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Login/LoginViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "474"
                  endingLineNumber = "474">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5064AF28-0CF4-4144-9927-51BE987F6CB3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Login/LoginViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "461"
            endingLineNumber = "461"
            landmarkName = "handleRegisterError(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C073E6F0-7B1F-480C-87B5-23CC993DC284"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "634"
            endingLineNumber = "634"
            landmarkName = "getUserInfo(completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D05DD795-32F9-444B-A5B7-AC1CC54E1FE9"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "583"
            endingLineNumber = "583"
            landmarkName = "requestVoid(_:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "920C12E1-8364-47D8-9355-AF8B50B87D40"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "508"
            endingLineNumber = "508"
            landmarkName = "request(_:model:retryCount:completion:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "920C12E1-8364-47D8-9355-AF8B50B87D40 - 795d4cec8875129c"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Result&lt;Moya.Response, Moya.MoyaError&gt;) -&gt; () in LookLock.NetworkManager.request&lt;&#x3c4;_0_0 where &#x3c4;_0_0: Swift.Decodable&gt;(_: LookLock.APIService, model: &#x3c4;_0_0.Type, retryCount: Swift.Int, completion: (Swift.Result&lt;&#x3c4;_0_0, Swift.Error&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Services/NetworkManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "509"
                  endingLineNumber = "509">
               </Location>
               <Location
                  uuid = "920C12E1-8364-47D8-9355-AF8B50B87D40 - 795d4cec8875129c"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Result&lt;Moya.Response, Moya.MoyaError&gt;) -&gt; () in LookLock.NetworkManager.request&lt;&#x3c4;_0_0 where &#x3c4;_0_0: Swift.Decodable&gt;(_: LookLock.APIService, model: &#x3c4;_0_0.Type, retryCount: Swift.Int, completion: (Swift.Result&lt;&#x3c4;_0_0, Swift.Error&gt;) -&gt; ()) -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Services/NetworkManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "509"
                  endingLineNumber = "509">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AEA7EDAA-D11E-4BD7-B277-7063F12EA7A1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManagerExtension.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "16"
            endingLineNumber = "16"
            landmarkName = "handleSessionExpired()"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "AEA7EDAA-D11E-4BD7-B277-7063F12EA7A1 - a453766ef85dee"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "LookLock.NetworkManager.handleSessionExpired() -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Services/NetworkManagerExtension.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "16"
                  endingLineNumber = "16">
               </Location>
               <Location
                  uuid = "AEA7EDAA-D11E-4BD7-B277-7063F12EA7A1 - d02d34f38de06d96"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 @Swift.MainActor () -&gt; () in LookLock.NetworkManager.handleSessionExpired() -&gt; ()"
                  moduleName = "LookLock.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Volumes/KKKJJJ/project/looklock/LookLock/Services/NetworkManagerExtension.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "18"
                  endingLineNumber = "18">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5822B62E-96D5-44C6-BFB1-6D7093CD45A7"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "512"
            endingLineNumber = "512"
            landmarkName = "request(_:model:retryCount:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "979C05EC-F5DB-441B-A87A-3BCA523EE276"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "539"
            endingLineNumber = "539"
            landmarkName = "request(_:model:retryCount:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7A1BAEB1-ABDE-4E20-B07B-96BB9B68379D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "550"
            endingLineNumber = "550"
            landmarkName = "request(_:model:retryCount:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "04AFABEB-F420-48A3-B191-0EDB54BB1651"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "LookLock/Services/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "556"
            endingLineNumber = "556"
            landmarkName = "request(_:model:retryCount:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
